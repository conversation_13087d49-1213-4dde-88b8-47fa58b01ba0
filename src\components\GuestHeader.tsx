import React, { useEffect } from 'react';
import { Layout, Select } from "antd";
import { assets } from "../assets/assets.ts";
import { useTranslation } from 'react-i18next';
import { Languages } from "lucide-react";

const { Option } = Select;
const { Header } = Layout;

const headerStyle: React.CSSProperties = {
    background: '#F6F7F8',
};
const GuestHeader: React.FC = () => {
    const { i18n } = useTranslation();

    // Récupérer la langue de préférence stockée dans localStorage (si elle existe)
    useEffect(() => {
        const storedLanguage = localStorage.getItem('language');
        if (storedLanguage) {
            i18n.changeLanguage(storedLanguage);
            document.documentElement.dir = storedLanguage === 'ar' ? 'rtl' : 'ltr';
        }
    }, [i18n]);

    const handleLanguageChange = (value: string) => {
        // Changer la langue avec i18n
        i18n.changeLanguage(value);

        // Mettre à jour la direction du texte
        document.documentElement.dir = value === 'ar' ? 'rtl' : 'ltr';

        // Sauvegarder la langue dans localStorage
        localStorage.setItem('language', value);
    };

    return (
        <Header style={headerStyle}>
            <div className="container mx-auto px-4 flex items-center justify-between h-full">
                <div className="flex items-center gap-2">
                    <img src={assets.logo} alt="logo" width={40} height={40} />
                    <span className="text-xl font-bold text-red-600"></span>
                </div>

                <Select
                    value={i18n.language}
                    onChange={handleLanguageChange}
                    className="language-select"

                    suffixIcon={<Languages size={16} />}
                >
                    <Option value="fr">Français</Option>
                    <Option value="ar">العربية</Option>
                </Select>
            </div>
        </Header>
    );
};

export default GuestHeader;
