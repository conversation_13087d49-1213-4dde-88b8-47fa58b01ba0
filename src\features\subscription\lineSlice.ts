import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import api from "../../config/axiosConfig.tsx";

const URL = "/lines";

const initialState = {
    items: [],
    paginatedItems: null,
    loading: false,
    error: null,
    currentItem: null,
    stationsAndRoutes: null
};

export const getLinesAll: any = createAsyncThunk(
  "getLinesAll",
  async (_: any, thunkAPI: any) => {
    try {
      //TODO AJOUTER FILTRE INACTIVE
      const url: string = `${URL}-all`;
      const resp: any = await api.get(url);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

export const getLines: any = createAsyncThunk(
  "getLines",
  async (
    data: {
      pageNumber: number;
      perPage: number;
      params: any;
      sort: any;
      filter: any;
    },
    thunkAPI: any
  ) => {
    try {
      const {
        nom_fr,
        nom_en,
        nom_ar,
        CODE_LINE,
        type_service,
        status,
        commercial_speed,
      } = data.params;
      const sort = data.sort;

      let url = `${URL}?page=${data.pageNumber}&perPage=${data.perPage}`;
      const searchParams = [];

      if (nom_fr) {
        searchParams.push(`nom_fr:${nom_fr}`);
      }
      if (nom_en) {
        searchParams.push(`nom_en:${nom_en}`);
      }
      if (nom_ar) {
        searchParams.push(`nom_ar:${nom_ar}`);
      }
      if (CODE_LINE) {
        searchParams.push(`CODE_LINE:${CODE_LINE}`);
      }
      if (type_service) {
        searchParams.push(`type_service:${type_service}`);
      }
      if (status !== undefined) {
        searchParams.push(`status:${status}`);
      }
      if (commercial_speed) {
        searchParams.push(`commercial_speed:${commercial_speed}`);
      }

      if (searchParams.length > 0) {
        url += `&search=${searchParams.join(";")}`;
      }

      const orderBy = [];
      const sortedBy = [];

      for (const [field, order] of Object.entries(sort)) {
        orderBy.push(field);
        sortedBy.push(order === "ascend" ? "asc" : "desc");
      }

      if (orderBy.length > 0) {
        url += `&orderBy=${orderBy.join(",")}&sortedBy=${sortedBy.join(",")}`;
      }
      const joint = "&searchJoin=and";
      url += joint;
      const resp: any = await api.get(url);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

export const storeLine: any = createAsyncThunk(
  "storeLine",
  async (data: any, thunkAPI: any) => {
    try {
      const url: string = `${URL}`;
      const resp: any = await api.post(url, data);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data);
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

export const updateLine: any = createAsyncThunk(
  "updateLine",
  async (data: any, thunkAPI: any) => {
    try {
      const { id, ...payload } = data;
      const url: string = `${URL}/${id}`;
      const resp: any = await api.put(url, payload);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data);
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

export const deleteLine: any = createAsyncThunk(
  "deleteLine",
  async (id: any, thunkAPI: any) => {
    try {
      const url: string = `${URL}/${id}`;
      const resp: any = await api.delete(url);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data.message);
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

export const assignLineStations: any = createAsyncThunk(
  "assignLineStations",
  async (
    data: {
      line_id: number;
      stations: Array<{
        id?: number;
        position: number;
        station_id: number;
        departure_times: Array<{
          season_id: number;
          times: string[];
        }>;
      }>;
      routes: Array<{
        id?: number;
        name: string;
        number_of_km: number;
        station_depart: number;
        station_arrival: number;
        inter_station: boolean;
        status: string;
      }>;
    },
    thunkAPI: any
  ) => {
    try {
      const url: string = `${URL}/assign-stations`;
      const resp: any = await api.post(url, data);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

export const getLineStationsAndRoutes: any = createAsyncThunk(
  "getLineStationsAndRoutes",
  async (lineId: number, thunkAPI: any) => {
    try {
      const url: string = `${URL}/${lineId}/stations-routes`;
      const resp: any = await api.get(url);
      return resp.data;
    } catch (error: any) {
      console.error("getLineStationsAndRoutes error:", error);
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

export const updateAssignLineStation: any = createAsyncThunk(
  "updateAssignLineStation",
  async (
    data: {
      line_id: number;
      stations: Array<{
        id?: number;
        position: number;
        id_station: number;
        departure_times: Array<{
          id_season: number;
          times: string[];
        }>;
      }>;
      routes: Array<{
        id?: number;
        number_of_km: number;
        id_station_start: number;
        id_station_end: number;
        inter_station: boolean;
        status: boolean;
      }>;
    },
    thunkAPI: any
  ) => {
    try {
      const url: string = `${URL}/${data.line_id}/stations-routes`;
      const resp: any = await api.put(url, data);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

export const getLinesByStations: any = createAsyncThunk(
  "getLinesByStations",
  async (
    params: {
      departureStationId: number;
      arrivalStationId: number;
      subsTypeId?: number;
    },
    thunkAPI: any
  ) => {
    try {
      const url: string = `${URL}/by-stations/${params.departureStationId}/${params.arrivalStationId}`;
      const resp: any = await api.get(url);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);


const lineSlice = createSlice({
    name: 'line',
    initialState,
    reducers: {
        setCurrentItem: (state, action) => {
            state.currentItem = action.payload;
        },
        clearError: (state) => {
            state.error = null;
        }
    },
    extraReducers: (builder) => {
        builder
            // getLinesAll
            .addCase(getLinesAll.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getLinesAll.fulfilled, (state, action) => {
                state.loading = false;
                state.items = action.payload;
            })
            .addCase(getLinesAll.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            // getLines
            .addCase(getLines.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getLines.fulfilled, (state, action) => {
                state.loading = false;
                state.paginatedItems = action.payload;
            })
            .addCase(getLines.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            // storeLine
            .addCase(storeLine.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(storeLine.fulfilled, (state:any, action) => {
                state.loading = false;
                if (state.items) {
                    state.items.push(action.payload);
                }
            })
            .addCase(storeLine.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            // updateLine
            .addCase(updateLine.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(updateLine.fulfilled, (state:any, action) => {
                state.loading = false;
                if (state.items) {
                    const index = state.items.findIndex((item:any) => item.id === action.payload.id);
                    if (index !== -1) {
                        state.items[index] = action.payload;
                    }
                }
            })
            .addCase(updateLine.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            // deleteLine
            .addCase(deleteLine.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(deleteLine.fulfilled, (state:any, action) => {
                state.loading = false;
                if (state.items) {
                    state.items = state.items.filter((item:any) => item.id !== action.payload);
                }
            })
            .addCase(deleteLine.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            // getLineStationsAndRoutes
            .addCase(getLineStationsAndRoutes.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getLineStationsAndRoutes.fulfilled, (state, action) => {
                state.loading = false;
                state.stationsAndRoutes = action.payload;
            })
            .addCase(getLineStationsAndRoutes.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            // assignLineStations and updateAssignLineStation
            .addCase(assignLineStations.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(assignLineStations.fulfilled, (state) => {
                state.loading = false;
            })
            .addCase(assignLineStations.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            .addCase(updateAssignLineStation.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(updateAssignLineStation.fulfilled, (state) => {
                state.loading = false;
            })
            .addCase(updateAssignLineStation.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            // getLinesByStations
            .addCase(getLinesByStations.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getLinesByStations.fulfilled, (state) => {
                state.loading = false;
            })
            .addCase(getLinesByStations.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            });
    }
});

export const { setCurrentItem, clearError } = lineSlice.actions;
export default lineSlice.reducer;
