

export const stationsData = [
    {
        id: 1,
        name: "<PERSON><PERSON>",
        delegation: {
            id: 1,
            name: "<PERSON><PERSON>"
        },
        governorate: {
            id: 1,
            name: "<PERSON><PERSON>"
        },
        created_at: "2024-02-01",
        station_type: "TERMINUS",
        latitude: 33.8806,
        longitude: 10.1647
    },
    {
        id: 2,
        name: "<PERSON><PERSON>",
        delegation: {
            id: 2,
            name: "<PERSON><PERSON>"
        },
        governorate: {
            id: 2,
            name: "<PERSON><PERSON>"
        },
        created_at: "2024-02-01",
        station_type: "INTER",
        latitude: 33.8907,
        longitude: 10.1892
    },
    {
        id: 3,
        name: "<PERSON>",
        delegation: {
            id: 3,
            name: "<PERSON>"
        },
        governorate: {
            id: 3,
            name: "<PERSON>"
        },
        created_at: "2024-02-02",
        station_type: "INTER",
        latitude: 35.8256,
        longitude: 10.6397
    },
    {
        id: 4,
        name: "<PERSON><PERSON><PERSON>",
        delegation: {
            id: 4,
            name: "Na<PERSON><PERSON>"
        },
        governorate: {
            id: 5,
            name: "<PERSON><PERSON><PERSON>"
        },
        created_at: "2024-02-02",
        station_type: "TERMINUS",
        latitude: 35.8256,
        longitude: 10.6397
    },
    {
        id: 5,
        name: "<PERSON><PERSON><PERSON>",
        delegation: {
            id: 4,
            name: "<PERSON><PERSON><PERSON>"
        },
        governorate: {
            id: 5,
            name: "Hammamet"
        },
        created_at: "2024-02-02",
        station_type: "HIDDEN",
        latitude: 35.8256,
        longitude: 10.6397
    }
];

export const AbnTypesData = [
    { id: 1, name: "Abonnement civil", color: "#CF5C78", created_at: "2023-07-20" },
    { id: 2, name: "Abonnement scolaire", color: "#FFE361", created_at: "2023-07-21" },
];

export const clientsData = [
    {
        id: 1,
        lastname: "Dupont",
        firstname: "Jean",
        client_type:"CIVIL",
        phone: "0601020304",
        address: "123 Rue Principale",
        cin: "AB123456",
        email: "<EMAIL>",
        created_at: "2023-07-20",
    },
    {
        id: 2,
        lastname: "Durand",
        firstname: "Marie",
        client_type:"CIVIL",
        phone: "0612345678",
        address: "456 Avenue République",
        cin: "CD789012",
        email: "<EMAIL>",
        created_at: "2023-07-21"
    },
    {
        id: 3,
        lastname: "Martin",
        firstname: "Paul",
        client_type:"CIVIL",
        phone: "0623456789",
        address: "789 Boulevard Saint-Michel",
        cin: "EF345678",
        email: "<EMAIL>",
        created_at: "2023-07-22"
    },
    {
        id: 4,
        lastname: "Dekhil",
        firstname: "Omran",
        client_type:"CIVIL",
        phone: "0623456789",
        address: "789 Boulevard Saint-Michel",
        cin: "EF345678",
        email: "<EMAIL>",
        created_at: "2023-07-22"
    },
];

export const clientSchoolData = [
    {
        id: 1,
        lastname: "Dupont",
        firstname: "Jean",
        client_type:"SCHOOL",
        phone: "0601020304",
        address: "123 Rue Principale",
        unique_student_ID: "12345678",
        cin: "12345678",
        email: "<EMAIL>",
        birthdate:"2000-01-01",
        sexe:'M',
        delegation: 1,
        establishment: 2,
        socialAffairsCode: "AB123456",
        created_at: "2023-07-20",
    },
    {
        id: 2,
        lastname: "Durand",
        firstname: "Marie",
        client_type:"UNIVERSITY",
        phone: "0612345678",
        address: "456 Avenue République",
        unique_student_ID: "99789012",
        cin: "99789012",
        email: "<EMAIL>",
        birthdate:"2000-02-02",
        sexe:'F',
        delegation: 2,
        establishment: 1,
        socialAffairsCode: null,
        created_at: "2023-07-21"
    },
    {
        id: 3,
        lastname: "Martin",
        firstname: "Paul",
        client_type:"UNIVERSITY",
        phone: "0623456789",
        address: "789 Boulevard Saint-Michel",
        unique_student_ID: "00345678",
        cin: "00345678",
        email: "<EMAIL>",
        birthdate:"2000-03-03",
        sexe:'M',
        delegation: 1,
        establishment: 1,
        socialAffairsCode: null,
        created_at: "2023-07-22"
    },
    {
        id: 4,
        lastname: "Dekhil",
        firstname: "Omran",
        phone: "0623456789",
        client_type:"UNIVERSITY",
        address: "789 Boulevard Saint-Michel",
        unique_student_ID: "12345679",
        cin: "12345679",
        email: "<EMAIL>",
        birthdate:"2000-04-04",
        sexe:'M',
        delegation: 2,
        establishment: 2,
        socialAffairsCode: null,
        created_at: "2023-07-22"
    },
];

export const line2Data = [
    {
        "id": 1,
        "code_line": "101",
        "nom_fr": "NABEUL-KAIROUAN",
        "nom_ar": "\u0646\u0627\u0628\u0644-\u0627\u0644\u0642\u064a\u0631\u0648\u0627\u0646",
        "nom_en": "NABEUL-KAIROUAN",
        "service_type": "NORMAL",
        "status": "ACTIF",
        "created_at": "2025-04-07T13:28:34.000000Z",
        "commercial_speed": "50.00",
        "stations": [
            {
                "id": 1,
                "nom_fr": "NABEUL",
                "nom_ar": "\u0646\u0627\u0628\u0644",
                "nom_en": "NABEUL",
                "position": 1,
                "departure_times": [
                    {
                        "id_season": 1,
                        "season_name": {
                            "nom_fr": "\u00c9t\u00e9",
                            "nom_en": "Summer",
                            "nom_ar": "\u0635\u064a\u0641"
                        },
                        "times": [
                            "14:00"
                        ]
                    },
                    {
                        "id_season": 2,
                        "season_name": {
                            "nom_fr": "Ramadhan",
                            "nom_en": "Ramadhan",
                            "nom_ar": "\u0631\u0645\u0636\u0627\u0646"
                        },
                        "times": [
                            "18:10"
                        ]
                    },
                    {
                        "id_season": 3,
                        "season_name": {
                            "nom_fr": "Standard",
                            "nom_en": "Standard",
                            "nom_ar": "\u0627\u0644\u0623\u064a\u0627\u0645 \u0627\u0644\u0639\u0627\u062f\u064a\u0629"
                        },
                        "times": [
                            "16:00"
                        ]
                    }
                ]
            },
            {
                "id": 18,
                "nom_fr": "TUNIS",
                "nom_ar": "\u062a\u0648\u0646\u0633",
                "nom_en": "TUNIS",
                "position": 2,
                "departure_times": []
            },
            {
                "id": 55,
                "nom_fr": "BENI KHALLED",
                "nom_ar": "\u0628\u0646\u064a \u062e\u0644\u0627\u062f",
                "nom_en": "BENI KHALLED",
                "position": 3,
                "departure_times": [
                    {
                        "id_season": 1,
                        "season_name": {
                            "nom_fr": "\u00c9t\u00e9",
                            "nom_en": "Summer",
                            "nom_ar": "\u0635\u064a\u0641"
                        },
                        "times": [
                            "14:00"
                        ]
                    },
                    {
                        "id_season": 2,
                        "season_name": {
                            "nom_fr": "Ramadhan",
                            "nom_en": "Ramadhan",
                            "nom_ar": "\u0631\u0645\u0636\u0627\u0646"
                        },
                        "times": [
                            "23:25"
                        ]
                    },
                    {
                        "id_season": 3,
                        "season_name": {
                            "nom_fr": "Standard",
                            "nom_en": "Standard",
                            "nom_ar": "\u0627\u0644\u0623\u064a\u0627\u0645 \u0627\u0644\u0639\u0627\u062f\u064a\u0629"
                        },
                        "times": [
                            "18:30"
                        ]
                    }
                ]
            }
        ],
        "routes": [
            {
                "id": 1,
                "number_of_km": "60.00",
                "station_depart": {
                    "id": 1,
                    "nom_fr": "NABEUL",
                    "nom_ar": "\u0646\u0627\u0628\u0644",
                    "nom_er": null
                },
                "station_arrival": {
                    "id": 18,
                    "nom_fr": "TUNIS",
                    "nom_ar": "\u062a\u0648\u0646\u0633",
                    "nom_en": "TUNIS"
                }
            },
            {
                "id": 2,
                "number_of_km": "10.00",
                "station_depart": {
                    "id": 18,
                    "nom_fr": "TUNIS",
                    "nom_ar": "\u062a\u0648\u0646\u0633",
                    "nom_er": null
                },
                "station_arrival": {
                    "id": 55,
                    "nom_fr": "BENI KHALLED",
                    "nom_ar": "\u0628\u0646\u064a \u062e\u0644\u0627\u062f",
                    "nom_en": "BENI KHALLED"
                }
            }
        ]
    }
];

export const routesData = [
    {
        id: 1,
        name: "Trajet 1",
        number_of_km: 25.0,
        status: "ACTIF",
        line : {
            id: 1,
            code_line: "L001",
            name: "Ariana-Nabeul",
            service_type: "NORMAL",
            status: "ACTIF",
            created_at: "2024-02-10T12:34:56Z",
            commercial_speed: 60,
            stations: [
                {
                    id: 1,
                    name: "Ariana",
                    order_in_line: 1,
                    departure_time: ["08:00","16:00"]
                },
                {
                    id: 2,
                    name: "Tunis",
                    order_in_line: 2,
                    departure_time: []
                },
                {
                    id: 3,
                    name: "Ben Arous",
                    order_in_line: 3,
                    departure_time: []
                },
                {
                    id: 4,
                    name: "Nabeul",
                    order_in_line: 4,
                    departure_time: ["12:00","14:00"]
                }
            ],
            routes: [
                {
                    id: 1,
                    is_regular: true,
                    status:true,
                    base_tariff: true,
                    inter_station:true,
                    number_of_km: 10,
                    station_depart: { id: 1, name: "Ariana" },
                    station_arrival: { id: 2, name: "Tunis" }
                },
                {
                    id: 2,
                    is_regular: true,
                    status:true,
                    base_tariff: 2,
                    inter_station:true,
                    number_of_km: 15,
                    station_depart: { id: 2, name: "Tunis" },
                    station_arrival: { id: 3, name: "Ben Arous" }
                },
                {
                    id: 3,
                    is_regular: true,
                    status:true,
                    base_tariff: 1,
                    inter_station:true,
                    number_of_km: 20,
                    station_depart: { id: 3, name: "Ben Arous" },
                    station_arrival: { id: 4, name: "Nabeul" },
                }
            ]
        },
        station_depart: {
            id: 1,
            name: "Station Alpha",
            station_type: "TERMINUS",
            latitude: 36.898556,
            longitude: 10.189567,
            delegation: {
                id: 1,
                name: "Delegation Nord",
                governorate: {
                    id: 1,
                    name: "Tunis"
                }
            }
        },
        station_arrival: {
            id: 2,
            name: "Station Beta",
            station_type: "INTER",
            latitude: 36.912345,
            longitude: 10.195432,
            delegation: {
                id: 2,
                name: "Delegation Sud",
                governorate: {
                    id: 1,
                    name: "Tunis"
                }
            }
        },
        tariff_options: [
            {
                abonnement_type: {
                    id: 1,
                    name: "Type 1",
                    color: "#3b82f6"
                },
                is_regular: true,
                base_tariff: {
                    id: 1,
                    name: "Tarif Standard",
                    tariff_per_km: 0.50,
                    date: "2024-01-01"
                }
            },
            {
                abonnement_type: {
                    id: 2,
                    name: "Type 2",
                    color: "#10b981"
                },
                is_regular: false,
                manual_tariff: 15.0
            }
        ],
        created_at: "2024-03-01 09:00:00",
        updated_at: "2024-03-05 14:30:00"
    },
    {
        id: 2,
        name: "Trajet 2",
        number_of_km: 30.5,
        status: "INACTIF",
        line : {
            id: 1,
            code_line: "L001",
            name: "Ariana-Nabeul",
            service_type: "NORMAL",
            status: "ACTIF",
            created_at: "2024-02-10T12:34:56Z",
            commercial_speed: 60,
            stations: [
                {
                    id: 1,
                    name: "Ariana",
                    order_in_line: 1,
                    departure_time: ["08:00","16:00"]
                },
                {
                    id: 2,
                    name: "Tunis",
                    order_in_line: 2,
                    departure_time: []
                },
                {
                    id: 3,
                    name: "Ben Arous",
                    order_in_line: 3,
                    departure_time: []
                },
                {
                    id: 4,
                    name: "Nabeul",
                    order_in_line: 4,
                    departure_time: ["12:00","14:00"]
                }
            ],
            routes: [
                {
                    id: 1,
                    is_regular: true,
                    status:true,
                    base_tariff: true,
                    inter_station:true,
                    number_of_km: 10,
                    station_depart: { id: 1, name: "Ariana" },
                    station_arrival: { id: 2, name: "Tunis" }
                },
                {
                    id: 2,
                    is_regular: true,
                    status:true,
                    base_tariff: 2,
                    inter_station:true,
                    number_of_km: 15,
                    station_depart: { id: 2, name: "Tunis" },
                    station_arrival: { id: 3, name: "Ben Arous" }
                },
                {
                    id: 3,
                    is_regular: true,
                    status:true,
                    base_tariff: 1,
                    inter_station:true,
                    number_of_km: 20,
                    station_depart: { id: 3, name: "Ben Arous" },
                    station_arrival: { id: 4, name: "Nabeul" },
                }
            ]
        },
        station_depart: {
            id: 1,
            name: "Station Gamma",
            station_type: "HIDDEN",
            latitude: 35.678912,
            longitude: 10.096543,
            delegation: {
                id: 3,
                name: "Delegation Est",
                governorate: {
                    id: 2,
                    name: "Sousse"
                }
            }
        },
        station_arrival: {
            id: 4,
            name: "Station Delta",
            station_type: "TERMINUS",
            latitude: 35.701234,
            longitude: 10.112345,
            delegation: {
                id: 4,
                name: "Delegation Ouest",
                governorate: {
                    id: 2,
                    name: "Sousse"
                }
            }
        },
        tariff_options: [
            {
                abonnement_type: {
                    id: 1,
                    name: "Abonnement Civil",
                    color: "#f59e0b"
                },
                is_regular: true,
                base_tariff: {
                    id: 1,
                    name: "Tarif VIP",
                    tariff_per_km: 0.80,
                    date: "2024-02-01"
                }
            },
            {
                abonnement_type: {
                    id: 33,
                    name: "Abonnement scolaire",
                    color: "#64748b"
                },
                is_regular: false,
                manual_tariff: 18.0
            }
        ],
        created_at: "2024-03-10 10:15:00",
        updated_at: "2024-03-12 16:45:00"
    }
];

export const baseTariffData = [
    {
        id: 1,
        name: "base 1",
        tariffPerKm: "0.8",
        abn_type: {
          id:1,
          name: "Abonnement scolaire"
        },
        date: "2023-07-20",
        created_at: "2023-07-20",
    },
    {
        id: 2,
        name: "base 2",
        tariffPerKm: "0.5",
        abn_type: {
            id:2,
            name: "Abonnement civil"
        },
        date: "2023-07-20",
        created_at: "2023-07-20",
    },
];

export const establishmentsData = [
    {
        id: 1,
        name: "FAC DES SCIENCES MATHS",
        short_name: "F.S.M.P.N.TUNIS",
        establishment_type: {
            id: 2,
            name: "Ecole secondaire"
        },
        delegation: {
            id: 1,
            name: "Tunis Centre"
        },
        created_at: "2023-07-20",
    },
    {
        id: 2,
        name: "FACULTE DE MEDECINE DE TUNIS",
        short_name: "F.MED.TUNIS",
        establishment_type: {
            id: 3,
            name: "Enseignement de base"
        },
        delegation: {
            id: 1,
            name: "Tunis Centre"
        },
        created_at: "2023-07-21",
    },
    {
        id: 3,
        name: "FAC DES SCIENCES DE BIZERTE",
        short_name: "F.S.BIZERTE",
        establishment_type: {
            id: 1,
            name: "Ecole primaire"
        },
        delegation: {
            id: 2,
            name: "Bizerte"
        },
        created_at: "2023-07-22",
    },
];

export const establishmentTypesData = [
    {
        id: 1,
        name: "Ecole primaire",
        created_at: "2023-07-20",
    },
    {
        id: 2,
        name: "Ecole secondaire",
        created_at: "2023-07-21",
    },
    {
        id: 3,
        name: "Enseignement de base",
        created_at: "2023-07-22",
    },
    {
        id: 4,
        name: "Enseignement universitaire",
        created_at: "2023-07-22",
    },
];

export  const  schoolDegreesData = [
    {
        id : 1,
        name: "1ère année primaire",
        establishment_type : {
            id: 1,
            name:"Ecole primaire"
        },
        age_max:"25",
        created_at: "2024-07-20",
    },
    {
        id : 2,
        name: "2ème année primaire",
        establishment_type : {
            id: 1,
            name:"Ecole primaire"
        },
        age_max: 18,
        created_at: "2023-07-20",
    }
]

export const campaignTypesData = [
    {
        id: 1,
        name: "Campagne scolaire",
        created_at: "2023-07-20",
    },
    {
        id: 2,
        name: "Campagne universitaire",
        created_at: "2023-07-21",
    },
    {
        id: 3,
        name: "Campagne civil",
        created_at: "2023-07-22",
    },
];

export const campaignsData = [
    {
        id: 1,
        name: "2025 - 1er trimestre",
        campaign_type:{
            id:3,
            name: "Campagne civil"
        },
        status: true,
    },
    {
        id: 2,
        name: "2025 - 2ème trimestre",
        campaign_type:{
            id:3,
            name: "Campagne civil"
        },
        status: false,
    },
    {
        id: 3,
        name: "2025 - 3ème trimestre",
        campaign_type:{
            id:3,
            name: "Campagne civil"
        },
        status: true,
    },
    {
        id: 4,
        name: "2025 - 4ème trimestre",
        campaign_type:{
            id:1,
            name: "Campagne scolaire"
        },
        status: false,
    },
    {
        id: 5,
        name: "2025 - Vacances d'été",
        campaign_type:{
            id:1,
            name: "Campagne scolaire"
        },
        status: true,
    },
];

export const SalesPeriods = [
    {
        id: 1,
        name: "Période d'inscription anticipée",
        start_date: "2023-07-15",
        end_date: "2023-08-15",
        status: false,
        campaign: {
            id: 1,
            name: "2025 - 1er trimestre",
            status: true,
            campaign_type: {
                id: 3,
                name: "Campagne civil"
            }
        }
    },
    {
        id: 2,
        name: "Période d'inscription principale",
        start_date: "2023-08-16",
        end_date: "2026-09-15",
        status: true,
        campaign: {
            id: 1,
            name: "2025 - 1er trimestre",
            status: true,
            campaign_type: {
                id: 3,
                name: "Campagne civil"
            }
        }
    }
];

export const governoratesData = [
    { id: 1, name: "Tunis", created_at: "2024-02-05" },
    { id: 2, name: "Ariana", created_at: "2024-02-05" },
    { id: 3, name: "Ben Arous", created_at: "2024-02-05" },
    { id: 4, name: "Manouba", created_at: "2024-02-05" },
    { id: 5, name: "Nabeul", created_at: "2024-02-05" },
];

export const delegationsData = [
    { id: 1, name: "Ariana Ville", governorate: { id: 1, name: "Ariana" }, created_at: "2024-02-05" },
    { id: 2, name: "Ettadhamen", governorate: { id: 1, name: "Ariana" }, created_at: "2024-02-05" },
    { id: 3, name: "Ben Arous", governorate: { id: 2, name: "Ben Arous" }, created_at: "2024-02-05" },
    { id: 4, name: "Radès", governorate: { id: 2, name: "Ben Arous" }, created_at: "2024-02-05" },
    { id: 5, name: "Tunis Centre", governorate: { id: 3, name: "Tunis" }, created_at: "2024-02-05" },
];

export const salesPointsData = [
    {
        id: 1,
        name: "Point vente 1",
        address: "10 Rue de Paris",
        phone: "0142030405",
        isActive: true,
        created_at: "2023-07-15",
        sales_period: {
            id: 1,
            name: "Période d'inscription anticipée",
            start_date: "2023-07-15",
            end_date: "2023-08-15",
            isOpen: false,
            campaign: {
                id: 1,
                name: "2025 - 1er trimestre",
                start_date: "2025-03-01",
                end_date: "2025-05-31",
                isOpen: true,
                campaign_type: {
                    id: 3,
                    name: "Campagne civil"
                }
            }
        },
        delegation: {
            id: 1,
            name: "Ariana Ville",
            governorate: {
                id: 1,
                name: "Ariana"
            }
        },
        agents:[
            {
                id: 1,
                lastname: "Dupont",
                firstname: "Jean",
                phone: "0601020304",
                address: "123 Rue Principale",
                cin: "AB123456",
                cards_number: 155,
                email: "<EMAIL>",
                created_at: "2023-07-20",
            },
            {
                id: 4,
                lastname: "Dekhil",
                firstname: "Omran",
                phone: "0623456789",
                address: "789 Boulevard Saint-Michel",
                cin: "EF345678",
                cards_number: 40,
                email: "<EMAIL>",
                created_at: "2023-07-22",
                sales_point_id: 1,
            },
        ]
    },
    {
        id: 2,
        name: "Point vente 2",
        address: "25 Avenue de la République",
        phone: "0472123456",
        isActive: true,
        created_at: "2023-08-01",
        sales_period: {
            id: 2,
            name: "Période d'inscription principale",
            start_date: "2023-08-16",
            end_date: "2023-09-15",
            isOpen: false,
            campaign: {
                id: 1,
                name: "2025 - 1er trimestre",
                start_date: "2025-03-01",
                end_date: "2025-05-31",
                isOpen: true,
                campaign_type: {
                    id: 3,
                    name: "Campagne civil"
                }
            }
        },
        delegation: {
            id: 2,
            name: "Ettadhamen",
            governorate: {
                id: 1,
                name: "Ariana"
            }
        },
        agents: [

        ]
    }
]

export const adminsData = [
    {
        id: 1,
        lastname: "Dupont",
        firstname: "Jean",
        phone: "0601020304",
        address: "123 Rue Principale",
        cin: "AB123456",
        email: "<EMAIL>",
        created_at: "2023-07-20",
        sales_point_id: 1,
    },
    {
        id: 2,
        lastname: "Durand",
        firstname: "Marie",
        phone: "0612345678",
        address: "456 Avenue République",
        cin: "CD789012",
        email: "<EMAIL>",
        created_at: "2023-07-21",
        sales_point_id: 2,
    },
    {
        id: 3,
        lastname: "Martin",
        firstname: "Paul",
        phone: "0623456789",
        address: "789 Boulevard Saint-Michel",
        cin: "EF345678",
        email: "<EMAIL>",
        created_at: "2023-07-22",
        sales_point_id: 1,
    },
    {
        id: 4,
        lastname: "Dekhil",
        firstname: "Omran",
        phone: "0623456789",
        address: "789 Boulevard Saint-Michel",
        cin: "EF345678",
        email: "<EMAIL>",
        created_at: "2023-07-22",
        sales_point_id: 1,
    },
];

export const assignAgentsData = [
    {
        id: 1,
        salesPoint: "point de vente 1",
        agent: `${adminsData[0].firstname} ${adminsData[0].lastname}`,
        salesPeriod: SalesPeriods[0].name,
        cards_number: 55,
        created_at: "2024-01-15",
    },
    {
        id: 2,
        salesPoint: "point de vente 2",
        agent: `${adminsData[1].firstname} ${adminsData[1].lastname}`,
        salesPeriod: SalesPeriods[1].name,
        cards_number: 44,
        created_at: "2024-04-10",
    },
    {
        id: 3,
        salesPoint: "point de vente 3",
        agent: `${adminsData[2].firstname} ${adminsData[2].lastname}`,
        salesPeriod: SalesPeriods[1].name,
        cards_number: 55,
        created_at: "2024-07-05",
    },
];

export const abnPeriodsData = [
    { id: 1, name: "Semaine", days_number: 7, created_at: "2024-01-15", },
    { id: 2, name: "Mensuelle", days_number: 30, created_at: "2024-01-15", },
    { id: 3, name: "Semestrielle", days_number: 180, created_at: "2024-01-15", },
    { id: 4, name: "Annuelle", days_number: 365, created_at: "2024-01-15", },
];

export const civilSubscriptionsData = [
    {
        id: 1,
        created_at: "2024-07-02",
        client:{
            id: 3,
            lastname: "Martin",
            firstname: "Paul",
            client_type:"CIVIL",
            phone: "0623456789",
            address: "789 Boulevard Saint-Michel",
            cin: "EF345678",
            email: "<EMAIL>",
            created_at: "2023-07-22"
        },
        station_depart: {
            id: 1,
            name: "Ariana",
            order_in_line: 1,
            departure_time: ["08:00","16:00"]
        },
        station_arrival: {
            id: 3,
            name: "Ben Arous",
            order_in_line: 3,
            departure_time: []
        },
        line: {
            id: 1,
            code_line: "L001",
            name: "Ariana-Nabeul",
            service_type: "NORMAL",
            status: "ACTIF",
            created_at: "2024-02-10T12:34:56Z",
            commercial_speed: 60,
            stations: [
                {
                    id: 1,
                    name: "Ariana",
                    order_in_line: 1,
                    departure_time: ["08:00","16:00"]
                },
                {
                    id: 2,
                    name: "Tunis",
                    order_in_line: 2,
                    departure_time: []
                },
                {
                    id: 3,
                    name: "Ben Arous",
                    order_in_line: 3,
                    departure_time: []
                },
                {
                    id: 4,
                    name: "Nabeul",
                    order_in_line: 4,
                    departure_time: ["12:00","14:00"]
                }
            ],
            routes: [
                {
                    id: 1,
                    is_regular: true,
                    base_tariff: 20,
                    number_of_km: 10,
                    station_depart: { id: 1, name: "Ariana" },
                    station_arrival: { id: 2, name: "Tunis" }
                },
                {
                    id: 2,
                    is_regular: true,
                    base_tariff: 20,
                    number_of_km: 15,
                    station_depart: { id: 2, name: "Tunis" },
                    station_arrival: { id: 3, name: "Ben Arous" }
                },
                {
                    id: 3,
                    is_regular: true,
                    base_tariff: 20,
                    number_of_km: 20,
                    station_depart: { id: 3, name: "Ben Arous" },
                    station_arrival: { id: 4, name: "Nabeul" },
                }
            ]
        },
        hasVacation: true,
        total_amount : 50
    },
];

export const subscriptionsData = [
    {
        id: 1,
        created_at: "2024-07-02",
        abn_type: {
            id: 1,
            name: "Abonnement civil",
            color: "#cf7689",
            hasCIN: true,
            is_student:false,
            is_impersonal: false,
            created_at: "2023-07-20"
        },
        client:{
            id: 3,
            lastname: "Martin",
            firstname: "Paul",
            phone: "0623456789",
            address: "789 Boulevard Saint-Michel",
            cin: "EF345678",
            email: "<EMAIL>",
            created_at: "2023-07-22"
        },
        station_depart: {
            id: 1,
            name: "Ariana",
            order_in_line: 1,
            departure_time: ["08:00","16:00"]
        },
        station_arrival: {
            id: 3,
            name: "Ben Arous",
            order_in_line: 3,
            departure_time: []
        },
        line:     {
            "id": 1,
            "code_line": "101",
            "nom_fr": "NABEUL-KAIROUAN",
            "nom_ar": "\u0646\u0627\u0628\u0644-\u0627\u0644\u0642\u064a\u0631\u0648\u0627\u0646",
            "nom_en": "NABEUL-KAIROUAN",
            "service_type": "NORMAL",
            "status": "ACTIF",
            "created_at": "2025-04-07T13:28:34.000000Z",
            "commercial_speed": "50.00",
            "stations": [
                {
                    "id": 1,
                    "nom_fr": "NABEUL",
                    "nom_ar": "\u0646\u0627\u0628\u0644",
                    "nom_en": "NABEUL",
                    "position": 1,
                    "departure_times": [
                        {
                            "id_season": 1,
                            "season_name": {
                                "nom_fr": "\u00c9t\u00e9",
                                "nom_en": "Summer",
                                "nom_ar": "\u0635\u064a\u0641"
                            },
                            "times": [
                                "14:00"
                            ]
                        },
                        {
                            "id_season": 2,
                            "season_name": {
                                "nom_fr": "Ramadhan",
                                "nom_en": "Ramadhan",
                                "nom_ar": "\u0631\u0645\u0636\u0627\u0646"
                            },
                            "times": [
                                "18:10"
                            ]
                        },
                        {
                            "id_season": 3,
                            "season_name": {
                                "nom_fr": "Standard",
                                "nom_en": "Standard",
                                "nom_ar": "\u0627\u0644\u0623\u064a\u0627\u0645 \u0627\u0644\u0639\u0627\u062f\u064a\u0629"
                            },
                            "times": [
                                "16:00"
                            ]
                        }
                    ]
                },
                {
                    "id": 18,
                    "nom_fr": "TUNIS",
                    "nom_ar": "\u062a\u0648\u0646\u0633",
                    "nom_en": "TUNIS",
                    "position": 2,
                    "departure_times": []
                },
                {
                    "id": 55,
                    "nom_fr": "BENI KHALLED",
                    "nom_ar": "\u0628\u0646\u064a \u062e\u0644\u0627\u062f",
                    "nom_en": "BENI KHALLED",
                    "position": 3,
                    "departure_times": [
                        {
                            "id_season": 1,
                            "season_name": {
                                "nom_fr": "\u00c9t\u00e9",
                                "nom_en": "Summer",
                                "nom_ar": "\u0635\u064a\u0641"
                            },
                            "times": [
                                "14:00"
                            ]
                        },
                        {
                            "id_season": 2,
                            "season_name": {
                                "nom_fr": "Ramadhan",
                                "nom_en": "Ramadhan",
                                "nom_ar": "\u0631\u0645\u0636\u0627\u0646"
                            },
                            "times": [
                                "23:25"
                            ]
                        },
                        {
                            "id_season": 3,
                            "season_name": {
                                "nom_fr": "Standard",
                                "nom_en": "Standard",
                                "nom_ar": "\u0627\u0644\u0623\u064a\u0627\u0645 \u0627\u0644\u0639\u0627\u062f\u064a\u0629"
                            },
                            "times": [
                                "18:30"
                            ]
                        }
                    ]
                }
            ],
            "routes": [
                {
                    "id": 1,
                    "number_of_km": "60.00",
                    "station_depart": {
                        "id": 1,
                        "nom_fr": "NABEUL",
                        "nom_ar": "\u0646\u0627\u0628\u0644",
                        "nom_er": null
                    },
                    "station_arrival": {
                        "id": 18,
                        "nom_fr": "TUNIS",
                        "nom_ar": "\u062a\u0648\u0646\u0633",
                        "nom_en": "TUNIS"
                    }
                },
                {
                    "id": 2,
                    "number_of_km": "10.00",
                    "station_depart": {
                        "id": 18,
                        "nom_fr": "TUNIS",
                        "nom_ar": "\u062a\u0648\u0646\u0633",
                        "nom_er": null
                    },
                    "station_arrival": {
                        "id": 55,
                        "nom_fr": "BENI KHALLED",
                        "nom_ar": "\u0628\u0646\u064a \u062e\u0644\u0627\u062f",
                        "nom_en": "BENI KHALLED"
                    }
                }
            ]
        },
        periodicity: {
            id: 2,
            name: "Mensuelle",
            days_number: 30,
            created_at: "2024-01-15"
        },
        rest_days:  {
            id: 1,
        },
        hasVacation: true,
        is_social_affair: false,
        total_amount : 50,
        status: 0
    },
    {
        id: 2,
        created_at: "2024-07-01",
        abn_type: {
            id: 1,
            name: "Abonnement civil",
            color: "#cf7689",
            hasCIN: true,
            is_student:false,
            is_impersonal: false,
            created_at: "2023-07-20"
        },
        client:{
            id: 3,
            lastname: "Ben Foulen",
            firstname: "Foulen",
            phone: "23456789",
            address: "789 Boulevard Saint-Michel",
            cin: "13345678",
            email: "<EMAIL>",
            created_at: "2023-07-01"
        },
        station_depart: {
            id: 1,
            name: "Ariana",
            order_in_line: 1,
            departure_time: ["08:00","16:00"]
        },
        station_arrival: {
            id: 3,
            name: "Ben Arous",
            order_in_line: 3,
            departure_time: []
        },
        line:     {
            "id": 1,
            "code_line": "101",
            "nom_fr": "NABEUL-KAIROUAN",
            "nom_ar": "\u0646\u0627\u0628\u0644-\u0627\u0644\u0642\u064a\u0631\u0648\u0627\u0646",
            "nom_en": "NABEUL-KAIROUAN",
            "service_type": "NORMAL",
            "status": "ACTIF",
            "created_at": "2025-04-07T13:28:34.000000Z",
            "commercial_speed": "50.00",
            "stations": [
                {
                    "id": 1,
                    "nom_fr": "NABEUL",
                    "nom_ar": "\u0646\u0627\u0628\u0644",
                    "nom_en": "NABEUL",
                    "position": 1,
                    "departure_times": [
                        {
                            "id_season": 1,
                            "season_name": {
                                "nom_fr": "\u00c9t\u00e9",
                                "nom_en": "Summer",
                                "nom_ar": "\u0635\u064a\u0641"
                            },
                            "times": [
                                "14:00"
                            ]
                        },
                        {
                            "id_season": 2,
                            "season_name": {
                                "nom_fr": "Ramadhan",
                                "nom_en": "Ramadhan",
                                "nom_ar": "\u0631\u0645\u0636\u0627\u0646"
                            },
                            "times": [
                                "18:10"
                            ]
                        },
                        {
                            "id_season": 3,
                            "season_name": {
                                "nom_fr": "Standard",
                                "nom_en": "Standard",
                                "nom_ar": "\u0627\u0644\u0623\u064a\u0627\u0645 \u0627\u0644\u0639\u0627\u062f\u064a\u0629"
                            },
                            "times": [
                                "16:00"
                            ]
                        }
                    ]
                },
                {
                    "id": 18,
                    "nom_fr": "TUNIS",
                    "nom_ar": "\u062a\u0648\u0646\u0633",
                    "nom_en": "TUNIS",
                    "position": 2,
                    "departure_times": []
                },
                {
                    "id": 55,
                    "nom_fr": "BENI KHALLED",
                    "nom_ar": "\u0628\u0646\u064a \u062e\u0644\u0627\u062f",
                    "nom_en": "BENI KHALLED",
                    "position": 3,
                    "departure_times": [
                        {
                            "id_season": 1,
                            "season_name": {
                                "nom_fr": "\u00c9t\u00e9",
                                "nom_en": "Summer",
                                "nom_ar": "\u0635\u064a\u0641"
                            },
                            "times": [
                                "14:00"
                            ]
                        },
                        {
                            "id_season": 2,
                            "season_name": {
                                "nom_fr": "Ramadhan",
                                "nom_en": "Ramadhan",
                                "nom_ar": "\u0631\u0645\u0636\u0627\u0646"
                            },
                            "times": [
                                "23:25"
                            ]
                        },
                        {
                            "id_season": 3,
                            "season_name": {
                                "nom_fr": "Standard",
                                "nom_en": "Standard",
                                "nom_ar": "\u0627\u0644\u0623\u064a\u0627\u0645 \u0627\u0644\u0639\u0627\u062f\u064a\u0629"
                            },
                            "times": [
                                "18:30"
                            ]
                        }
                    ]
                }
            ],
            "routes": [
                {
                    "id": 1,
                    "number_of_km": "60.00",
                    "station_depart": {
                        "id": 1,
                        "nom_fr": "NABEUL",
                        "nom_ar": "\u0646\u0627\u0628\u0644",
                        "nom_er": null
                    },
                    "station_arrival": {
                        "id": 18,
                        "nom_fr": "TUNIS",
                        "nom_ar": "\u062a\u0648\u0646\u0633",
                        "nom_en": "TUNIS"
                    }
                },
                {
                    "id": 2,
                    "number_of_km": "10.00",
                    "station_depart": {
                        "id": 18,
                        "nom_fr": "TUNIS",
                        "nom_ar": "\u062a\u0648\u0646\u0633",
                        "nom_er": null
                    },
                    "station_arrival": {
                        "id": 55,
                        "nom_fr": "BENI KHALLED",
                        "nom_ar": "\u0628\u0646\u064a \u062e\u0644\u0627\u062f",
                        "nom_en": "BENI KHALLED"
                    }
                }
            ]
        },
        periodicity: {
            id: 2,
            name: "Mensuelle",
            days_number: 30,
            created_at: "2024-01-15"
        },
        rest_days:  {
            id: 1,
        },
        hasVacation: true,
        is_social_affair: false,
        total_amount : 50,
        status: 1
    },
    {
        id: 3,
        created_at: "2024-06-30",
        abn_type: {
            id: 1,
            name: "Abonnement civil",
            color: "#cf7689",
            hasCIN: true,
            is_student:false,
            is_impersonal: false,
            created_at: "2023-07-20"
        },
        client:{
            id: 3,
            lastname: "Ben Salem",
            firstname: "Salem",
            phone: "23456780",
            address: "789 Boulevard Saint-Michel",
            cin: "13345699",
            email: "<EMAIL>",
            created_at: "2023-07-01"
        },
        station_depart: {
            id: 1,
            name: "Ariana",
            order_in_line: 1,
            departure_time: ["08:00","16:00"]
        },
        station_arrival: {
            id: 3,
            name: "Ben Arous",
            order_in_line: 3,
            departure_time: []
        },
        line:     {
            "id": 1,
            "code_line": "101",
            "nom_fr": "NABEUL-KAIROUAN",
            "nom_ar": "\u0646\u0627\u0628\u0644-\u0627\u0644\u0642\u064a\u0631\u0648\u0627\u0646",
            "nom_en": "NABEUL-KAIROUAN",
            "service_type": "NORMAL",
            "status": "ACTIF",
            "created_at": "2025-04-07T13:28:34.000000Z",
            "commercial_speed": "50.00",
            "stations": [
                {
                    "id": 1,
                    "nom_fr": "NABEUL",
                    "nom_ar": "\u0646\u0627\u0628\u0644",
                    "nom_en": "NABEUL",
                    "position": 1,
                    "departure_times": [
                        {
                            "id_season": 1,
                            "season_name": {
                                "nom_fr": "\u00c9t\u00e9",
                                "nom_en": "Summer",
                                "nom_ar": "\u0635\u064a\u0641"
                            },
                            "times": [
                                "14:00"
                            ]
                        },
                        {
                            "id_season": 2,
                            "season_name": {
                                "nom_fr": "Ramadhan",
                                "nom_en": "Ramadhan",
                                "nom_ar": "\u0631\u0645\u0636\u0627\u0646"
                            },
                            "times": [
                                "18:10"
                            ]
                        },
                        {
                            "id_season": 3,
                            "season_name": {
                                "nom_fr": "Standard",
                                "nom_en": "Standard",
                                "nom_ar": "\u0627\u0644\u0623\u064a\u0627\u0645 \u0627\u0644\u0639\u0627\u062f\u064a\u0629"
                            },
                            "times": [
                                "16:00"
                            ]
                        }
                    ]
                },
                {
                    "id": 18,
                    "nom_fr": "TUNIS",
                    "nom_ar": "\u062a\u0648\u0646\u0633",
                    "nom_en": "TUNIS",
                    "position": 2,
                    "departure_times": []
                },
                {
                    "id": 55,
                    "nom_fr": "BENI KHALLED",
                    "nom_ar": "\u0628\u0646\u064a \u062e\u0644\u0627\u062f",
                    "nom_en": "BENI KHALLED",
                    "position": 3,
                    "departure_times": [
                        {
                            "id_season": 1,
                            "season_name": {
                                "nom_fr": "\u00c9t\u00e9",
                                "nom_en": "Summer",
                                "nom_ar": "\u0635\u064a\u0641"
                            },
                            "times": [
                                "14:00"
                            ]
                        },
                        {
                            "id_season": 2,
                            "season_name": {
                                "nom_fr": "Ramadhan",
                                "nom_en": "Ramadhan",
                                "nom_ar": "\u0631\u0645\u0636\u0627\u0646"
                            },
                            "times": [
                                "23:25"
                            ]
                        },
                        {
                            "id_season": 3,
                            "season_name": {
                                "nom_fr": "Standard",
                                "nom_en": "Standard",
                                "nom_ar": "\u0627\u0644\u0623\u064a\u0627\u0645 \u0627\u0644\u0639\u0627\u062f\u064a\u0629"
                            },
                            "times": [
                                "18:30"
                            ]
                        }
                    ]
                }
            ],
            "routes": [
                {
                    "id": 1,
                    "number_of_km": "60.00",
                    "station_depart": {
                        "id": 1,
                        "nom_fr": "NABEUL",
                        "nom_ar": "\u0646\u0627\u0628\u0644",
                        "nom_er": null
                    },
                    "station_arrival": {
                        "id": 18,
                        "nom_fr": "TUNIS",
                        "nom_ar": "\u062a\u0648\u0646\u0633",
                        "nom_en": "TUNIS"
                    }
                },
                {
                    "id": 2,
                    "number_of_km": "10.00",
                    "station_depart": {
                        "id": 18,
                        "nom_fr": "TUNIS",
                        "nom_ar": "\u062a\u0648\u0646\u0633",
                        "nom_er": null
                    },
                    "station_arrival": {
                        "id": 55,
                        "nom_fr": "BENI KHALLED",
                        "nom_ar": "\u0628\u0646\u064a \u062e\u0644\u0627\u062f",
                        "nom_en": "BENI KHALLED"
                    }
                }
            ]
        },
        periodicity: {
            id: 2,
            name: "Mensuelle",
            days_number: 30,
            created_at: "2024-01-15"
        },
        rest_days:  {
            id: 1,
        },
        hasVacation: true,
        is_social_affair: false,
        total_amount : 50,
        status: 2
    },
];


