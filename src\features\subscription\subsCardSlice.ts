import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import api from "../../config/axiosConfig.tsx";

const URL = '/subs-cards';

const initialState = {
    items: [],
    paginatedItems: null,
    loading: false,
    error: null,
    currentItem: null,
    subscriptionCards: null
};


export const getSubsCardsAll: any = createAsyncThunk(
    "getSubsCardsAll",
    async (_:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}-all`;
            const resp:any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);


export const getSubsCardsBySubscription:any = createAsyncThunk(
    "getSubsCardsBySubscription",
    async (subsId:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}/by-subscription/${subsId}`;
            const resp:any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);


export const storeSubsCard: any = createAsyncThunk(
    "storeSubsCard",
    async (data:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}`;
            const resp:any = await api.post(url, data);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);


const subsCardSlice = createSlice({
    name: 'subsCard',
    initialState,
    reducers: {
        setCurrentItem: (state, action) => {
            state.currentItem = action.payload;
        },
        clearError: (state) => {
            state.error = null;
        }
    },
    extraReducers: (builder) => {
        builder
            // get subsCards by subscription
            .addCase(getSubsCardsBySubscription.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getSubsCardsBySubscription.fulfilled, (state, action) => {
                state.loading = false;
                state.subscriptionCards = action.payload;
            })
            .addCase(getSubsCardsBySubscription.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })

            // store subsCard
            .addCase(storeSubsCard.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(storeSubsCard.fulfilled, (state) => {
                state.loading = false;
            })
            .addCase(storeSubsCard.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })

    }
});

export const { setCurrentItem, clearError } = subsCardSlice.actions;
export default subsCardSlice.reducer;