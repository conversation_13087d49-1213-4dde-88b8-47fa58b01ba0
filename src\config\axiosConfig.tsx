import axios from 'axios';

const api = axios.create({
    baseURL: import.meta.env.VITE_API_URL_BACKEND,
});

api.interceptors.request.use((config) => {
    const token = localStorage.getItem('TOKEN');
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    config.headers['Accept-Language'] = localStorage.getItem('language') || 'fr';
    return config;
});

api.interceptors.response.use(
    (response) => response,
    (error) => {
        if (error.response?.status === 401) {
            localStorage.removeItem('TOKEN');
            window.location.href = '/';
        }
        if (error.response?.status === 403) {
            window.location.href = '/unauthorized';
        }
        return Promise.reject(error);
    }
);


export default api;
