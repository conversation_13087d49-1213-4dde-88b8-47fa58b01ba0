import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import api from "../../config/axiosConfig.tsx";

const URL = "/sales-periods";

export type ISalePeriod = {
    id: number;
    nom_fr: string;
    nom_en: string;
    nom_ar: string;
    date_start: string;
    date_end: string;
    id_campaign: number;
    id_abn_type: number;
    status: boolean;
  };

const initialState = {
    items: [],
    loading: false,
    error: null,
    currentItem: null
};

export const getSalesPeriodsAll: any = createAsyncThunk(
    "getSalesPeriodsAll",
    async (_: any, thunkAPI: any) => {
        try {
            const url: string = `${URL}-all`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            return thunkAPI.rejectWithValue("An unexpected error occurred.");
        }
    }
);

const salesPeriodsSlice = createSlice({
    name: 'salesPeriods',
    initialState,
    reducers: {
        setCurrentItem: (state, action) => {
            state.currentItem = action.payload;
        },
        clearError: (state) => {
            state.error = null;
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(getSalesPeriodsAll.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getSalesPeriodsAll.fulfilled, (state, action) => {
                state.loading = false;
                state.items = action.payload;
            })
            .addCase(getSalesPeriodsAll.rejected, (state:any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            });
    }
});

export const { setCurrentItem, clearError } = salesPeriodsSlice.actions;
export default salesPeriodsSlice.reducer;