@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200..1000&display=swap');

/* Utilisation d'une classe unique pour éviter les conflits avec d'autres composants */
.subs-receipt-component {
    font-family: 'Cairo', sans-serif;
    padding: 20px;
    direction: rtl;
}

.subs-receipt-component .header {
    text-align: right;
    padding-bottom: 10px;
    margin-bottom: 20px;
    border-bottom: 3px solid #BC0202;
}

.subs-receipt-component .company-name {
    font-size: 16px;
    font-weight: bold;
}

.subs-receipt-component .payment-method {
    font-size: 16px;
    margin-bottom: 10px;
}

.subs-receipt-component .date {
    text-align: left;
    margin-bottom: 20px;
}

.subs-receipt-component .section-title {
    font-weight: bold;
    margin: 15px 0 10px 0;
}

.subs-receipt-component .fw-semimedium {
    font-weight: 600 !important;
}

.subs-receipt-component .cart-info {
    border: 1px solid #ddd;
    padding: 10px;
}

.subs-receipt-component table {
    width: 100%;
    border-collapse: collapse;
    margin: 10px 0;
}

.subs-receipt-component table,
.subs-receipt-component th,
.subs-receipt-component td {
    border: 1px solid #ddd;
}

.subs-receipt-component th,
.subs-receipt-component td {
    padding: 8px;
    text-align: right;
}

.subs-receipt-component .amount {
    font-weight: bold;
    font-size: 18px;
    margin-top: 20px;
}

.subs-receipt-component .cut-img {
    position: relative;
    top: -33px;
    transform: rotateY(180deg);
}

/* Styles pour améliorer la lisibilité du QR code */
.subs-receipt-component .qr-code {
    image-rendering: pixelated;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
}

@media print {
    .subs-receipt-component .qr-code {
        print-color-adjust: exact;
        -webkit-print-color-adjust: exact;
    }
}