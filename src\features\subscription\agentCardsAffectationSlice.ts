import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import api from "../../config/axiosConfig.tsx";

const URL = '/agent-cards-affectations';

const initialState = {
    items: [],
    paginatedItems: null,
    loading: false,
    error: null,
    currentItem: null
};

export const getAgentCardsAffectationAll: any = createAsyncThunk(
    "getAgentCardsAffectationAll",
    async (_:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}-all`;
            const resp:any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getAgentCardsAffectations: any = createAsyncThunk(
    "getAgentCardsAffectations",
    async (
        data: {
            pageNumber: number;
            perPage: number;
            params: any,
            sort: any,
            filter: any
        },
        thunkAPI:any
    ) => {
        try {
            const { 
                agent_id,
                sale_point_id,
                sales_period_id,
                cards_number,
                status,
                created_at,
                updated_at
            } = data.params;
            
            const sort = data.sort;

            let url = `${URL}?page=${data.pageNumber}&perPage=${data.perPage}`;
            const searchParams = [];

            if (agent_id) {
                searchParams.push(`agent_id:${agent_id}`);
            }
            if (sale_point_id) {
                searchParams.push(`sale_point_id:${sale_point_id}`);
            }
            if (sales_period_id) {
                searchParams.push(`sales_period_id:${sales_period_id}`);
            }
            if (cards_number) {
                searchParams.push(`cards_number:${cards_number}`);
            }
            if (status !== undefined) {
                searchParams.push(`status:${status}`);
            }
            if (created_at) {
                searchParams.push(`created_at:${created_at}`);
            }
            if (updated_at) {
                searchParams.push(`updated_at:${updated_at}`);
            }

            if (searchParams.length > 0) {
                url += `&search=${searchParams.join(';')}`;
            }

            const orderBy = [];
            const sortedBy = [];

            for (const [field, order] of Object.entries(sort)) {
                orderBy.push(field);
                sortedBy.push(order === 'ascend' ? 'asc' : 'desc');
            }

            if (orderBy.length > 0) {
                url += `&orderBy=${orderBy.join(',')}&sortedBy=${sortedBy.join(',')}`;
            }
            const joint = "&searchJoin=and";
            url += joint;
            const resp:any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const storeAgentCardsAffectation: any = createAsyncThunk(
    "storeAgentCardsAffectation",
    async (data:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}`;
            const resp:any = await api.post(url, data);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const updateAgentCardsAffectation: any = createAsyncThunk(
    "updateAgentCardsAffectation",
    async (data: any, thunkAPI:any) => {
        try {
            const { id, ...payload } = data;
            const url:string = `${URL}/${id}`;
            const resp:any = await api.put(url, payload);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const deleteAgentCardsAffectation: any = createAsyncThunk(
    "deleteAgentCardsAffectation",
    async (id:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}/${id}`;
            const resp:any = await api.delete(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data.message);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getAgentsBySalePoint: any = createAsyncThunk(
    "getAgentsBySalePoint",
    async ({ salePointId, salePeriodId }: { salePointId: number, salePeriodId: number }, thunkAPI: any) => {
        try {
            const url: string = `${URL}/by-point/${salePointId}/${salePeriodId}`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

const agentCardsAffectationSlice = createSlice({
    name: 'agentCardsAffectation',
    initialState,
    reducers: {
        setCurrentItem: (state, action) => {
            state.currentItem = action.payload;
        },
        clearError: (state) => {
            state.error = null;
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(getAgentCardsAffectationAll.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getAgentCardsAffectationAll.fulfilled, (state, action) => {
                state.loading = false;
                state.items = action.payload;
            })
            .addCase(getAgentCardsAffectationAll.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })            
    }
});

export const { setCurrentItem, clearError } = agentCardsAffectationSlice.actions;
export default agentCardsAffectationSlice.reducer;