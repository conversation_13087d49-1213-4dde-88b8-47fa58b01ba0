import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import api from "../../config/axiosConfig.tsx";

const URL = '/agencies';

const initialState = {
    items: [],
    paginatedItems: null,
    loading: false,
    error: null,
    currentItem: null,
    salesPoints: []
};

export const getAgencyAll: any = createAsyncThunk(
    "getAgencyAll",
    async (_:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}-all`;
            const resp:any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getAgencies: any = createAsyncThunk(
    "getAgencies",
    async (
        data: {
            pageNumber: number;
            perPage: number;
            params: any,
            sort: any,
            filter: any
        },
        thunkAPI:any
    ) => { 
        try {
            const { 
                nom_fr, 
                nom_en, 
                nom_ar, 
                code,
                contact,
                address,
                id_delegation,
                id_governorate 
            } = data.params;
            console.log(data.params);
            const sort = data.sort;

            let url = `${URL}?page=${data.pageNumber}&perPage=${data.perPage}`;
            const searchParams = [];

            if (nom_fr) {
                searchParams.push(`nom_fr:${nom_fr}`);
            }
            if (nom_en) {
                searchParams.push(`nom_en:${nom_en}`);
            }
            if (nom_ar) {
                searchParams.push(`nom_ar:${nom_ar}`);
            }
            if (code) {
                searchParams.push(`code:${code}`);
            }
            if (contact) {
                searchParams.push(`contact:${contact}`);
            }
            if (address) {
                searchParams.push(`address:${address}`);
            }
            if (id_delegation) {
                searchParams.push(`id_delegation:${id_delegation}`);
            }
            if (id_governorate) {
                searchParams.push(`id_governorate:${id_governorate}`);
            }

            if (searchParams.length > 0) {
                url += `&search=${searchParams.join(';')}`;
            }

            const orderBy = [];
            const sortedBy = [];

            for (const [field, order] of Object.entries(sort)) {
                orderBy.push(field);
                sortedBy.push(order === 'ascend' ? 'asc' : 'desc');
            }

            if (orderBy.length > 0) {
                url += `&orderBy=${orderBy.join(',')}&sortedBy=${sortedBy.join(',')}`;
            }
            const joint = "&searchJoin=and";
            url += joint;
            const resp:any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const storeAgency: any = createAsyncThunk(
    "storeAgency",
    async (data:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}`;
            const resp:any = await api.post(url, data);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const updateAgency: any = createAsyncThunk(
    "updateAgency",
    async (data: any, thunkAPI:any) => {
        try {
            const { id, ...payload } = data;
            const url:string = `${URL}/${id}`;
            const resp:any = await api.put(url, payload);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const deleteAgency: any = createAsyncThunk(
    "deleteAgency",
    async (id:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}/${id}`;
            const resp:any = await api.delete(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data.message);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getAgencySalesPoints: any = createAsyncThunk(
    "getAgencySalesPoints",
    async (agencyId: number, thunkAPI: any) => {
        try {
            const url: string = `${URL}/${agencyId}/sales-points`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

const agencySlice = createSlice({
    name: 'agency',
    initialState,
    reducers: {
        setCurrentItem: (state, action) => {
            state.currentItem = action.payload;
        },
        clearError: (state) => {
            state.error = null;
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(getAgencyAll.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getAgencyAll.fulfilled, (state, action) => {
                state.loading = false;
                state.items = action.payload;
            })
            .addCase(getAgencyAll.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            .addCase(getAgencies.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getAgencies.fulfilled, (state, action) => {
                state.loading = false;
                state.paginatedItems = action.payload;
            })
            .addCase(getAgencies.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            .addCase(storeAgency.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(storeAgency.fulfilled, (state: any, action) => {
                state.loading = false;
                state.items.push(action.payload);
            })
            .addCase(storeAgency.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            .addCase(updateAgency.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(updateAgency.fulfilled, (state: any, action) => {
                state.loading = false;
                const index = state.items.findIndex((item: any) => item.id === action.payload.id);
                if (index !== -1) {
                    state.items[index] = action.payload;
                }
            })
            .addCase(updateAgency.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            .addCase(deleteAgency.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(deleteAgency.fulfilled, (state: any, action) => {
                state.loading = false;
                state.items = state.items.filter((item: any) => item.id !== action.payload);
            })
            .addCase(deleteAgency.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            .addCase(getAgencySalesPoints.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getAgencySalesPoints.fulfilled, (state, action) => {
                state.loading = false;
                state.salesPoints = action.payload;
            })
            .addCase(getAgencySalesPoints.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            });
    }
});

export const { setCurrentItem, clearError } = agencySlice.actions;
export default agencySlice.reducer;