import React, { lazy, Suspense, useEffect, useState } from "react";
import {ConfigProvider} from "antd";
import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import { useSelector } from "react-redux";
import './i18n';
import frFR from "antd/locale/fr_FR";
import arEG from "antd/locale/ar_EG";
// i18n translated documents
import {useTranslation} from "react-i18next";
import {ResetPassword} from "./pages/auth";
import { useDispatch } from "react-redux";
import { AppDispatch } from "./features/store";
import { checkAuthStatus } from "./features/auth/authSlice";
import useRefreshToken from "./hooks/useRefreshToken.tsx";

const AuthLayout = lazy(() => import("./layouts/AuthLayout"));
const GuestLayout = lazy(() => import("./layouts/GuestLayout"));
const AccessDenied = lazy(() => import("./pages/auth/AccessDenied"));
const Login = lazy(() => import("./pages/auth/Login"));
const Register = lazy(() => import("./pages/auth/Register"));
const NotFound = lazy(() => import("./pages/auth/NotFound"));
const Loading = lazy(() => import("./components/Loading"));
const ManageProfile = lazy(() => import("./pages/profile/ManageProfile"));
const CampaignsList = lazy(() => import("./pages/subscriptions/CampaignsList"));
const SubscriptionList = lazy(() => import("./pages/subscriptions/SubscriptionList"));
const QrCodeDataView = lazy(() => import("./pages/subscriptions/QrCodeDataView"));


const App: React.FunctionComponent = () => {
     const { i18n } = useTranslation();
    useRefreshToken();

    const dispatch = useDispatch<AppDispatch>();
    const { isAuthenticated } = useSelector((state: any) => state.auth);

    const [loading, setLoading] = useState(true);

    useEffect(() => {
        dispatch(checkAuthStatus());
    }, [dispatch]);

    useEffect(() => {
        if (isAuthenticated !== null) {
            setLoading(false);
        }
    }, [isAuthenticated]);

    if (loading) {
        return <Loading />;
    }


    /*|--------------------------------------------------------------------------
    | Routes accessibles pour tous les utilisateurs
    |-------------------------------------------------------------------------- */
    const GuestRoutes = (
        <Route path="/" element={<GuestLayout />}>
            <Route index element={<Login />} />
            <Route path="/register" element={<Register />} />
            <Route path="/reset-password" element={<ResetPassword />} />
            <Route path="/not-found" element={<NotFound />} />
            <Route path="/access-denied" element={<AccessDenied />} />
            <Route path="/subscription-verify" element={
                <Suspense fallback={<Loading />}>
                    <QrCodeDataView />
                </Suspense>
            } />
            <Route path="*" element={<NotFound />} />
        </Route>
    );

    /*|--------------------------------------------------------------------------
    | Routes accessibles pour les utilisateurs authentifiés
    |-------------------------------------------------------------------------- */
    const AuthRoutes = (
        <Route
            path="/auth/*"
            element={
                isAuthenticated ? (
                    <AuthLayout />
                ) : (
                    <Navigate to="/access-denied" replace />
                )
            }
        >
            <Route path="profile" element={
                <Suspense fallback={<Loading  />}>
                    <ManageProfile />
                </Suspense>
            }/>
            <Route path="new-subscription" element={
                <Suspense fallback={<Loading  />}>
                    <CampaignsList />
                </Suspense>
            }/>
            <Route path="my-subscriptions" element={
                <Suspense fallback={<Loading />}>
                    <SubscriptionList />
                </Suspense>
            } />
            <Route path="access-denied" element={<AccessDenied />} />
            <Route path="*" element={<NotFound />} />
        </Route>
    );


    return (
        <ConfigProvider
            theme={{
                token: {
                    colorPrimary: '#dc2626',
                },
            }}
            locale={i18n.language === 'ar' ? arEG : i18n.language === 'fr' ? frFR : frFR  }
            direction={i18n.language === 'ar' ? 'rtl' : 'ltr'}
        >
            <Router>
                <Suspense fallback={<Loading />}>
                    <Routes>
                        {GuestRoutes}
                        {AuthRoutes}
                    </Routes>
                </Suspense>
            </Router>
        </ConfigProvider>
    );
};

export default App;
