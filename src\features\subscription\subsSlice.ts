import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import api from "../../config/axiosConfig.tsx";

const URL = "/subscriptions";

const initialState = {
  items: [],
  paginatedItems: null,
  loading: false,
  error: null,
  currentItem: null,
  lastSubscription: null,
  lastSubscriptionLoading: false
};

export const getSubscriptionsAll: any = createAsyncThunk(
  "getSubscriptionsAll",
  async (_: any, thunkAPI: any) => {
    try {
      const url: string = `${URL}-all`;
      const resp: any = await api.get(url);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

export const getSubscriptions: any = createAsyncThunk(
    "getSubscriptions",
    async (
      data: {
        pageNumber: number;
        perPage: number;
        params: any;
        sort: any;
        filter: any;
      },
      thunkAPI: any
    ) => {
      try {
        const {id_subs_type, status, created_at} = data.params;

        const sort = data.sort;
        let url = `${URL}?page=${data.pageNumber}&perPage=${data.perPage}`;

        const searchParams = [];

        if (id_subs_type) {
          searchParams.push(`id_subs_type:${id_subs_type}`);
        }
        // Add status filter (PAYED, NOTPAYED, CANCELED, PENDINF)
        if (status) {
          searchParams.push(`status:${status}`);
        }

        // Handle date range filtering for created_at field in params
        if (created_at && Array.isArray(created_at) && created_at.length === 2) {
          const [startDate, endDate] = created_at;
          if (startDate && endDate) {
            // Format dates to YYYY-MM-DD format for backend
            const formattedStartDate = startDate.format ? startDate.format('YYYY-MM-DD') : startDate;
            const formattedEndDate = endDate.format ? endDate.format('YYYY-MM-DD') : endDate;

            // Add date range as search parameters
            searchParams.push(`created_at_start:${formattedStartDate}`);
            searchParams.push(`created_at_end:${formattedEndDate}`);
          }
        }

        if (searchParams.length > 0) {
          url += `&search=${searchParams.join(";")}`;
        }

        const orderBy = [];
        const sortedBy = [];
        for (const [field, order] of Object.entries(sort)) {
          orderBy.push(field);
          sortedBy.push(order === "ascend" ? "asc" : "desc");
        }
        if (orderBy.length > 0) {
          url += `&orderBy=${orderBy.join(",")}&sortedBy=${sortedBy.join(",")}`;
        }

        if (data.filter && Object.keys(data.filter).length > 0) {
          const filterParams = [];
          for (const [key, value] of Object.entries(data.filter)) {
            if (value !== undefined && value !== null && value !== '') {
              filterParams.push(`${key}:${value}`);
            }
          }
          if (filterParams.length > 0) {
            url += `&filter=${filterParams.join(";")}`;
          }
        }
        const joint = "&searchJoin=and";
        url += joint;
        const resp: any = await api.get(url);
        return resp.data;
      } catch (error: any) {
        const { data } = error.response;
        return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
      }
    }
  );

export const storeSubscription: any = createAsyncThunk(
  "storeSubscription",
  async (data: any, thunkAPI: any) => {
    try {
      const url: string = `${URL}`;
      const formData = new FormData();
      const { photo, rest_days, ...otherData } = data;

      Object.keys(otherData).forEach((key) => {
        if (otherData[key] !== undefined && otherData[key] !== null) {
          formData.append(key, String(otherData[key]));
        }
      });

      if (Array.isArray(rest_days)) {
        rest_days.forEach((day: string | number) => {
          formData.append("rest_days[]", String(day));
        });
      }

      if (photo) {
        formData.append("photo", photo);
      }

      const resp = await api.post(url, formData);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

export const updateSubscription: any = createAsyncThunk(
  "updateSubscription",
  async (data: any, thunkAPI: any) => {
    try {
      const { id, photo, rest_days,  ...otherData } = data;
      const url: string = `${URL}/${id}`;
      const formData = new FormData();

       if (Array.isArray(rest_days)) {
        rest_days.forEach((day: string | number) => {
          formData.append("rest_days[]", String(day));
        });
      }

      console.log(otherData)

      Object.keys(otherData).forEach((key) => {
        if (otherData[key] !== undefined && otherData[key] !== null) {
          formData.append(key, String(otherData[key]));
        }
      });


      if (photo) {
        if (photo instanceof File) {
          formData.append("photo", photo, "subscription_photo.jpg");
        }
        else if (photo instanceof Blob) {
          const fileToUpload = new File([photo], "subscription_photo.jpg", {
            type: photo.type || "image/jpeg",
          });
          formData.append("photo", fileToUpload, "subscription_photo.jpg");
        }
        else if (typeof photo === "string" && (photo.startsWith("data:") || photo.startsWith("blob:"))) {
          try {
            const response = await fetch(photo);
            const blob = await response.blob();
            const fileToUpload = new File([blob], "subscription_photo.jpg", {
              type: "image/jpeg",
            });
            formData.append("photo", fileToUpload, "subscription_photo.jpg");
          } catch (error) {
            formData.append("photo", photo);
          }
        }
        else if (typeof photo === "string") {
          formData.append("photo", photo);
        }
      }

      formData.append("_method", "PUT");

      const resp: any = await api.post(url, formData);
      return resp.data;
    } catch (error: any) {
      console.error("Error in updateSubscription:", error);
      if (error.response?.status === 403) {
        window.location.href = "/unauthorized";
      }
      return thunkAPI.rejectWithValue(
        error.response?.data || "An unexpected error occurred."
      );
    }
  }
);

export const deleteSubscription: any = createAsyncThunk(
  "deleteSubscription",
  async (id: any, thunkAPI: any) => {
    try {
      const url: string = `${URL}/${id}`;
      const resp: any = await api.delete(url);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data.message);
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

export const verifySocialAffairId = createAsyncThunk(
  "verifySocialAffairId",
  async (id: string, thunkAPI: any) => {
    try {
      // Then verify in API
      const apiResponse = await api.get(`/social-affairs/verify/${id}`);
      if (!apiResponse.data.exists) {
        return thunkAPI.rejectWithValue("ID not found in system");
      }

      return { success: true };
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

export const storeClientWithSubscription: any = createAsyncThunk(
  "storeClientWithSubscription",
  async (data: any, thunkAPI: any) => {
    try {
      const url: string = `${URL}`;
      const formData = new FormData();
      const { photo, rest_days, clientData, ...otherData } = data;

      // Add client data as JSON
      if (clientData) {
        formData.append("clientData", clientData);
      }

        // Handle rest_days as an array
        if (Array.isArray(rest_days)) {
          rest_days.forEach((day: string | number) => {
            formData.append("rest_days[]", String(day));
          });
        }

      // Handle other subscription data
      Object.keys(otherData).forEach((key) => {
        if (otherData[key] !== undefined && otherData[key] !== null) {
          formData.append(key, String(otherData[key]));
        }
      });

      // Handle photo separately if it exists
      if (photo) {
        formData.append("photo", photo);
      }

      const resp = await api.post(url, formData);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data);
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);


export const searchCitoyen: any = createAsyncThunk(
    "searchCitoyen",
    async (data: { cin: string, jourNaiss: any, moisNaiss: any, anneeNaiss: any }, thunkAPI: any) => {
        console.log(data);

      try {
        const url: string = `${URL}/verif/civile`;
        const resp: any = await api.post(url, {
            cin: data.cin,
            jourNaiss: data.jourNaiss,
            moisNaiss: data.moisNaiss,
            anneeNaiss: data.anneeNaiss
        });
        return resp.data;
      } catch (error: any) {
        if (error.response) {
          const { status, data } = error.response;
          if (status === 403) {
            window.location.href = '/unauthorized';
          }
          return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
        } else {
          return thunkAPI.rejectWithValue("An unexpected error occurred.");
        }
      }
    }
  );

  export const searchStudentWithCIN: any = createAsyncThunk(
    "searchStudentWithCIN",
    async (data: { id_etud: string, date_naissance: any }, thunkAPI: any) => {
      try {
        const url: string = `${URL}/verif/etudiant`;
        const resp: any = await api.post(url, {
            id_etud: data.id_etud,
            date_naissance: data.date_naissance
        });
        return resp.data;
      } catch (error: any) {
        if (error.response) {
          const { status, data } = error.response;
          if (status === 403) {
            window.location.href = '/unauthorized';
          }
          return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
        } else {
          return thunkAPI.rejectWithValue("An unexpected error occurred.");
        }
      }
    }
  );

  export const searchStudentWithoutCIN: any = createAsyncThunk(
    "searchStudentWithoutCIN",
    async (data: { identifiant: string, date_naissance: any }, thunkAPI: any) => {
      try {
        // const url: string = `${URL}/verif/eleve`;
        const url: string = `${URL}/verif/eleve`;
        const resp: any = await api.post(url, {
            identifiant: data.identifiant,
            date_naissance: data.date_naissance
        });
        return resp.data;
      } catch (error: any) {
        if (error.response) {
          const { status, data } = error.response;
          if (status === 403) {
            window.location.href = '/unauthorized';
          }
          return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
        } else {
          return thunkAPI.rejectWithValue("An unexpected error occurred.");
        }
      }
    }
  );

export const getLastSubscriptionByClient: any = createAsyncThunk(
  "getLastSubscriptionByClient",
  async (_, thunkAPI: any) => {
    try {
      const url: string = `${URL}/last`;
      const resp: any = await api.get(url);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = '/unauthorized';
        }
        return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

const subscriptionSlice = createSlice({
  name: 'subscription',
  initialState,
  reducers: {
    setCurrentItem: (state, action) => {
      state.currentItem = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(getLastSubscriptionByClient.pending, (state) => {
        state.lastSubscriptionLoading = true;
        state.error = null;
      })
      .addCase(getLastSubscriptionByClient.fulfilled, (state, action) => {
        state.lastSubscriptionLoading = false;
        state.lastSubscription = action.payload;
      })
      .addCase(getLastSubscriptionByClient.rejected, (state, action) => {
        state.lastSubscriptionLoading = false;
        state.error = action.payload || "An error occurred";
      });
  }
});

export const { setCurrentItem, clearError } = subscriptionSlice.actions;
export default subscriptionSlice.reducer;
