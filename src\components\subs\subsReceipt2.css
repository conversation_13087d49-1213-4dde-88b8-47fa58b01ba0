  @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200..1000&display=swap');
.subs-receipt-component2 {
    font-family: 'Cairo', sans-serif;
    padding: 20px;
    direction: rtl;
}
.subs-receipt-component2 .header {
    text-align: right;
    padding-bottom: 10px;
    margin-bottom: 20px;
    border-bottom: 3px solid #BC0202;
}
.subs-receipt-component2 .company-name {
    font-size: 16px;
    font-weight: bold;
}
.subs-receipt-component2 .payment-method {
    font-size: 16px;
    margin-bottom: 10px;
}
.subs-receipt-component2 .subs-receipt-component2 .date {
    text-align: left;
    margin-bottom: 20px;
}
.subs-receipt-component2 .section-title {
    font-weight: bold;
    margin: 15px 0 10px 0;
}
.subs-receipt-component2 .fw-semimedium {
    font-weight: 600 !important;
}
.subs-receipt-component2 .cart-info{
    border: 1px solid #ddd;
    padding: 10px;
}
.subs-receipt-component2 table {
    width: 100%;
    border-collapse: collapse;
    margin: 10px 0;
}
.subs-receipt-component2 table,
.subs-receipt-component2 th,
.subs-receipt-component2 td {
    border: 1px solid #ddd;
}
.subs-receipt-component2 th,
.subs-receipt-component2 td {
    padding: 8px;
    text-align: right;
}
.subs-receipt-component2 .amount {
    font-weight: bold;
    font-size: 18px;
    margin-top: 20px;
}
.subs-receipt-component2 .cut-img {
    position: relative;
    top: -33px;
    transform: rotateY(180deg);
}