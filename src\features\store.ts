import { configureStore } from '@reduxjs/toolkit';
import authReducer from './auth/authSlice.ts';
import salesPeriodsReducer from './subscription/salesPeriodsSlice.ts';
import i118nReducer from './i118n/i118nSlice.ts';
import governorateReducer from './subscription/governorateSlice.ts';
import periodicityReducer from './subscription/periodicitySlice.ts';
import establishmentReducer from './subscription/establishmentSlice.ts';
import schoolDegreeReducer from './subscription/schoolDegreeSlice.ts';
import delegationReducer from './subscription/delegationSlice.ts';
import purchaseOrderReducer from './subscription/purchaseOrderSlice.ts';
import lineReducer from './subscription/lineSlice.ts';
import adminReducer from './subscription/adminSlice.ts';
import discountReducer from './subscription/discountSlice.ts';
import seasonReducer from './subscription/seasonSlice.ts';
import configReducer from './subscription/configSlice.ts';
import agencyReducer from './subscription/agencySlice.ts';
import establishmentTypeReducer from './subscription/establishmentTypeSlice.ts';
import tarifBaseReducer from './subscription/tarifBaseSlice.ts';
import paymentMethodReducer from './subscription/paymentMethodsSlice.ts';
import cardFeesReducer from './subscription/cardFeeSlice.ts';
import salePointReducer from './subscription/salePointSlice.ts';
import cardTypeReducer from './subscription/cardTypeSlice.ts';
import locationTypeReducer from './subscription/locationTypeSlice.ts';
import stockSliceReducer from './subscription/stockCardSlice.ts';
import academicYearReducer from './subscription/academicYearSlice.ts';
import agentCardsAffectationReducer from './subscription/agentCardsAffectationSlice.ts';
import cardSequenceTrackingReducer from './subscription/cardSequenceTrackingSlice.ts';
import subsCardSliceReducer from './subscription/subsCardSlice.ts';
import abnTypeReducer from './subscription/abnTypeSlice.ts';
import subscriptionReducer from './subscription/subsSlice.ts';

const store = configureStore({
    reducer: {
        auth: authReducer,
        i118n: i118nReducer,
        salesPeriod: salesPeriodsReducer,
        governorate: governorateReducer,
        periodicity: periodicityReducer,
        establishment: establishmentReducer,
        schoolDegree: schoolDegreeReducer,
        delegation: delegationReducer,
        academicYear: academicYearReducer,
        purchaseOrder: purchaseOrderReducer,
        line: lineReducer,
        admin: adminReducer,
        discount: discountReducer,
        season: seasonReducer,
        config: configReducer,
        agency: agencyReducer,
        establishmentType: establishmentTypeReducer,
        tarifBase: tarifBaseReducer,
        abnType: abnTypeReducer,
        paymentMethod: paymentMethodReducer,
        cardFee: cardFeesReducer,
        salePoint: salePointReducer,
        cardType: cardTypeReducer,
        locationType: locationTypeReducer,
        stockCard:stockSliceReducer,
        agentCardsAffectation: agentCardsAffectationReducer,
        cardSequenceTracking: cardSequenceTrackingReducer,
        subsCard: subsCardSliceReducer,
        subscription: subscriptionReducer,
    },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export default store;