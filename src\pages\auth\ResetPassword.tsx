import React, { useState } from "react";
import { Form, Input, Button, Card, Steps, Divider } from "antd";
import { Mail, KeyRound } from "lucide-react";
import { useTranslation } from "react-i18next";
import { assets } from "../../assets/assets.ts";
import { Link, useNavigate } from "react-router-dom";
import { OTPProps } from "antd/es/input/OTP";
import { useMediaQuery } from "react-responsive";
import { LanguageSelector } from "../../components";
import { useDispatch, useSelector } from "react-redux";
import {
    resetPassword,
  resetPasswordStepOne,
  resetPasswordStepTwo,
} from "../../features/auth/authSlice.ts";
import { toast, ToastContainer } from "react-toastify";

const { Step } = Steps;

const ResetPassword: React.FC = () => {
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const [form] = Form.useForm();
  const dispatch: any = useDispatch();
  const { loading } = useSelector((state: any) => state.auth);
  const [currentStep, setCurrentStep] = useState(0);
  const [countdownEmail, setCountdownEmail] = useState(0);
  const [isEmailLinkActive, setIsEmailLinkActive] = useState(true);
  const [otpEmailValue, setOtpEmailValue] = useState("");
  const [otpEmailError, setOtpEmailError] = useState(false);
  const [isEmailValidated, setIsEmailValidated] = useState(false);
  const [allFormFields, setAllFormFields] = useState({});

  const isSmallScreen: any = useMediaQuery({ query: "(max-width: 575px)" });

  const onChangeOtpEmail: OTPProps["onChange"] = (text) => {
    setOtpEmailError(false);
    setOtpEmailValue(text);
  };

  // Fonction pour renvoyer le code par téléEmail
  const handleResendEmailCode = () => {
    // Logique pour renvoyer le code par téléEmail
    setIsEmailLinkActive(false);
    setCountdownEmail(60); // Démarre un compte à rebours de 60 secondes
    // Logique d'envoi du code (par exemple, via une API)
    const interval = setInterval(() => {
      setCountdownEmail((prev: any) => {
        if (prev === 1) {
          clearInterval(interval);
          setIsEmailLinkActive(true);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
    // handle step 1
    dispatch(resetPasswordStepOne(allFormFields)).unwrap();
  };

  const steps = [
    {
      title: t("reset.step1Title"),
      content: (
        <>
          <h2 className="text-2xl text-center font-bold text-gray-800 mb-2">
            {t("reset.requestTitle")}
          </h2>
          <p className="text-gray-600 text-center mb-6">
            {t("reset.requestDescription")}
          </p>
          <Form.Item
            name="email"
            rules={[
              { required: true, message: t("reset.validation.emailRequired") },
              { type: "email", message: t("reset.validation.emailValid") },
            ]}
          >
            <Input
              prefix={<Mail className="text-gray-400" size={18} />}
              placeholder={t("reset.emailPlaceholder")}
              className="text-sm h-12"
              disabled={isEmailValidated}
            />
          </Form.Item>
        </>
      ),
    },
    {
      title: t("reset.step2Title"),
      content: (
        <>
          <h2 className="text-2xl text-center font-bold text-gray-800 mb-2">
            {t("reset.OTPEmailTitle")}
          </h2>
          <p className="text-gray-600 text-center">
            {t("reset.OTPEmailDescription")}
          </p>
          <Form.Item validateStatus={otpEmailError ? "error" : ""}>
            <div className="lg:px-16 sm:px-3 pt-6">
              {isEmailValidated ? (
                <div className="text-sm p-6 border-2 border-gray-100 rounded-lg shadow-sm">
                  <div className="flex justify-center items-center">
                    <img src={assets.validatedGif} alt="GIF" width={70} />
                  </div>
                  <div className="flex items-center justify-center text-red-600">
                    {t("reset.EmailValidated")}
                  </div>
                </div>
              ) : (
                <div className="text-center justify-center items-center">
                  <Input.OTP
                    length={8}
                    size="middle"
                    className={`justify-center ${
                      otpEmailError ? "border-red-500" : ""
                    }`}
                    autoFocus
                    onChange={onChangeOtpEmail}
                  />

                  <div className="mt-3 flex text-center justify-center items-center">
                    <span>
                      {isEmailLinkActive ? (
                        <Button
                          type="link"
                          className="text-red-500 text-sm"
                          onClick={handleResendEmailCode}
                        >
                          {t("reset.resendCode")}
                        </Button>
                      ) : (
                        <span>
                          {t("reset.retryIn")}{" "}
                          <span className="font-semibold">
                            {countdownEmail}
                          </span>{" "}
                          {t("reset.seconds")}
                        </span>
                      )}
                    </span>
                  </div>
                </div>
              )}
            </div>
          </Form.Item>
        </>
      ),
    },
    {
      title: t("reset.step3Title"),
      content: (
        <>
          <h2 className="text-2xl text-center font-bold text-gray-800 mb-2">
            {t("reset.newPasswordTitle")}
          </h2>
          <p className="text-gray-600 text-center mb-6">
            {t("reset.newPasswordDescription")}
          </p>
          <Form.Item
            name="password"
            rules={[
              {
                required: true,
                message: t("reset.validation.passwordRequired"),
              },
              { min: 8, message: t("reset.validation.passwordLength") },
              {
                pattern:
                  /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[+-.!@\$%\^&\*()])/,
                message: t("reset.validation.passwordComplexity"),
              },
            ]}
          >
            <Input.Password
              prefix={<KeyRound className="text-gray-400" size={18} />}
              placeholder={t("reset.passwordPlaceholder")}
              className="text-sm h-12"
            />
          </Form.Item>
          <Form.Item
            name="confirmPassword"
            dependencies={["password"]}
            rules={[
              {
                required: true,
                message: t("reset.validation.confirmRequired"),
              },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue("password") === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(
                    new Error(t("reset.validation.passwordMatch"))
                  );
                },
              }),
            ]}
          >
            <Input.Password
              prefix={<KeyRound className="text-gray-400" size={18} />}
              placeholder={t("reset.confirmPlaceholder")}
              className="text-sm h-12"
            />
          </Form.Item>
        </>
      ),
    },
  ];

  // Handle input errors messages
  const handleInputErrors: any = (error: any) => {
    const apiErrors = error.errors;
    // Transformez en tableau pour setFields
    const fields = Object.entries(apiErrors).map(([fieldName, messages]) => ({
      name: fieldName,
      errors: messages as string[],
    }));
    form.setFields(fields);
  };

  const next = () => {
    form
      .validateFields()
      .then(() => {
        switch (currentStep) {
          case 0:
            // handle step 1
            dispatch(resetPasswordStepOne(form.getFieldsValue()))
              .unwrap()
              .then((response: any) => {
                setAllFormFields(form.getFieldsValue());
                setCurrentStep((prev: any) => prev + 1);
              })
              .catch((error: any) => {
                handleInputErrors(error);
                toast.error(error.message);
              });
            break;
          case 1:
            // handle step 2
            dispatch(
              resetPasswordStepTwo({
                ...allFormFields,
                verificationCode: otpEmailValue,
              })
            )
              .unwrap()
              .then((response: any) => {
                setAllFormFields({
                  ...allFormFields,
                  verificationCode: otpEmailValue,
                });
                setIsEmailValidated(true);
                setCurrentStep((prev: any) => prev + 1);
              })
              .catch((error: any) => {
                setOtpEmailError(true);
                toast.error(error.message);
              });
            break;
        }
        // if (currentStep === 1 && !isEmailValidated) {
        //     return validateOtpEmail()
        //         .then(() => setCurrentStep((prev:any) => prev + 1))
        //         .catch((error:any) => console.error(error.message));
        // } else if(currentStep < steps.length -1) {
        //     setCurrentStep((prev:any) => prev + 1);
        // } else {
        //     onFinish()
        // }
      })
      .catch((errorInfo: any) => {
        console.log("Validation Failed:", errorInfo);
      });
  };

  const onFinish = () => {
    const allValues = form.getFieldsValue(true);
    // handle reset password
    dispatch(resetPassword({ ...allFormFields, ...allValues }))
      .unwrap()
      .then((response: any) => {
        navigate("/");
      })
      .catch((error: any) => {
        toast.error(error.message);
      });
  };

  const prev = () => {
    // setOtpEmailValue("");
    if (currentStep === 0 || (currentStep === 1 && isEmailValidated)) {
      return; // Empêche de revenir à l'étape précédente si email validé
    }
    setCurrentStep((prev: any) => prev - 1);
  };
  const isPrevDisabled =
    currentStep === 0 || (currentStep === 1 && isEmailValidated);

  return (
    <div className="min-h-screen flex">
      <ToastContainer />
      {/*|--------------------------------------------------------------------------
            |  - LOGIN FORM
            |-------------------------------------------------------------------------- */}
      <div
        className="
                    w-full
                    md:w-full
                    flex items-center justify-center
                    bg-center bg-no-repeat bg-contain
                    "
        style={{
          backgroundImage: `url(${assets.bg})`,
        }}
      >
        <Card
          className="w-full max-w-full  md:max-w-2xl lg:max-w-3xl m-5 !drop-shadow-sm py-20 lg:py-8"
          bordered={false}
        >
          {/*|--------------------------------------------------------------------------
                        |  - SELECT LANGUAGES
                        |-------------------------------------------------------------------------- */}
          <div
            className={`absolute top-5 ${
              i18n.language === "ar" ? "right-5" : "left-5"
            } `}
          >
            <LanguageSelector />
          </div>

          <Steps current={currentStep} className="mb-8 mt-8">
            {steps.map((item, index) => (
              <Step key={index} title={isSmallScreen ? item.title : ""} />
            ))}
          </Steps>

          <Form
            form={form}
            name="reset"
            initialValues={{ remember: true }}
            onFinish={onFinish}
            layout="vertical"
            size="large"
            onValuesChange={(changedValues) => {
              const fieldName = Object.keys(changedValues)[0];
              form.setFields([{ name: fieldName, errors: [] }]);
            }}
          >
            {steps[currentStep].content}

            <div className="flex justify-between">
              {currentStep === 1 && (
                <Button
                  disabled={isPrevDisabled || loading}
                  type="link"
                  onClick={prev}
                  className="h-10 text-black"
                >
                  {t("reset.previous")}
                </Button>
              )}
              {currentStep === 0 ? (
                <Button
                  type="primary"
                  onClick={next}
                  className="h-12 w-full mx-auto block"
                  loading={loading}
                >
                  {t("reset.next")}
                </Button>
              ) : currentStep === 1 ? (
                <Button
                  type="link"
                  onClick={next}
                  className="h-10 text-red-500 !hover:text-red-500"
                  disabled={loading}
                >
                  {t("reset.next")}
                </Button>
              ) : (
                <Button
                  disabled={loading}
                  type="primary"
                  htmlType="submit"
                  className="h-12 w-full mx-auto block"
                >
                  {t("reset.confirm")}
                </Button>
              )}
            </div>

            <Divider className="border-gray-200">
              <span className="text-gray-400 text-sm">
                {t("login.or") || "OR"}
              </span>
            </Divider>
            <div className="text-gray-600 mt-6 text-center">
              {t("reset.haveAccount")}
              <Link
                to="/"
                className="mx-1 text-red-600 hover:text-red-500 hover:underline transition-colors font-medium"
              >
                {t("reset.signIn")}
              </Link>
            </div>
          </Form>
        </Card>
      </div>

      {/* Right side - Image */}
      {/* <LandingSide/> */}
    </div>
  );
};

export default ResetPassword;
