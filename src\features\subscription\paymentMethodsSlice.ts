import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import api from "../../config/axiosConfig.tsx";

const URL = "/payment-methods";

const initialState = {
    items: [],
    paginatedItems: null,
    loading: false,
    error: null,
    currentItem: null
};

export const getPaymentMethodsAll: any = createAsyncThunk(
  "getPaymentMethodsAll",
  async (_: any, thunkAPI: any) => {
    try {
      const url: string = `${URL}-all`;
      const resp: any = await api.get(url);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

export const getPaymentMethods: any = createAsyncThunk(
  "getPaymentMethods",
  async (
    data: {
      pageNumber: number;
      perPage: number;
      params: any;
      sort: any;
      filter: any;
    },
    thunkAPI: any
  ) => {
    try {
      const { nom_fr, nom_en, nom_ar, status } = data.params;
      const sort = data.sort;

      let url = `${URL}?page=${data.pageNumber}&perPage=${data.perPage}`;
      const searchParams = [];

      if (nom_fr) {
        searchParams.push(`nom_fr:${nom_fr}`);
      }
      if (nom_en) {
        searchParams.push(`nom_en:${nom_en}`);
      }
      if (nom_ar) {
        searchParams.push(`nom_ar:${nom_ar}`);
      }
      if (status !== undefined) {
        searchParams.push(`status:${status}`);
      }

      if (searchParams.length > 0) {
        url += `&search=${searchParams.join(";")}`;
      }

      const orderBy = [];
      const sortedBy = [];

      for (const [field, order] of Object.entries(sort)) {
        orderBy.push(field);
        sortedBy.push(order === "ascend" ? "asc" : "desc");
      }

      if (orderBy.length > 0) {
        url += `&orderBy=${orderBy.join(",")}&sortedBy=${sortedBy.join(",")}`;
      }
      const joint = "&searchJoin=and";
      url += joint;
      const resp = await api.get(url);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data);
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

export const storePaymentMethod: any = createAsyncThunk(
  "storePaymentMethod",
  async (data: any, thunkAPI: any) => {
    try {
      const url: string = `${URL}`;
      const resp: any = await api.post(url, data);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data);
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

export const updatePaymentMethod: any = createAsyncThunk(
  "updatePaymentMethod",
  async (data: any, thunkAPI: any) => {
    try {
      const { id, ...payload } = data;
      const url: string = `${URL}/${id}`;
      const resp: any = await api.put(url, payload);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data);
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

export const deletePaymentMethod: any = createAsyncThunk(
  "deletePaymentMethod",
  async (id: number, thunkAPI: any) => {
    try {
      const url: string = `${URL}/${id}`;
      const resp: any = await api.delete(url);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data);
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

const paymentMethodsSlice = createSlice({
    name: 'paymentMethod',
    initialState,
    reducers: {
        setCurrentItem: (state, action) => {
            state.currentItem = action.payload;
        },
        clearError: (state) => {
            state.error = null;
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(getPaymentMethodsAll.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getPaymentMethodsAll.fulfilled, (state, action) => {
                state.loading = false;
                state.items = action.payload;
            })
            .addCase(getPaymentMethodsAll.rejected, (state:any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            });
    }
});

export const { setCurrentItem, clearError } = paymentMethodsSlice.actions;
export default paymentMethodsSlice.reducer;
