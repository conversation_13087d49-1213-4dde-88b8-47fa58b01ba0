import { Button, Result } from "antd";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { assets } from "../../assets/assets.ts";

const NotFound = () => {
    const navigate = useNavigate();
    const { t } = useTranslation();

    const handleGoHome = () => {
        navigate('/');
    };

    return (
        <div className="flex items-center justify-center min-h-screen bg-white">
            <Result
                title={t('not_found.title')}
                subTitle={t('not_found.sub_title')}
                icon={
                    <div className="flex justify-center items-center">
                        <img
                            src={assets.notFound}
                            alt={t('not_found.title')}
                            className="w-1/3 text-center"
                        />
                    </div>
                }
                extra={
                    <Button
                        type="primary"
                        onClick={handleGoHome}
                        className="bg-red-500 border-red-500 text-white hover:bg-red-600 hover:border-red-600 font-bold py-2 px-6"
                    >
                        {t('not_found.button')}
                    </Button>
                }
            />
        </div>
    );
};

export default NotFound;
