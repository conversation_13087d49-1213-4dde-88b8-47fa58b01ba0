import React, {useEffect, useState} from 'react';
import {Form, Input, Button, Card, Divider, Tooltip, Tabs, Select, Space, Spin} from 'antd';
import {Mail, Lock, Phone, HelpCircle, IdCard, Loader, RefreshCcw} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import {Link, useNavigate} from "react-router-dom";
import countryOptions from "../../data/countryPhoneCodes.ts";
import {LanguageSelector} from "../../components";
import {toast, ToastContainer} from "react-toastify";
import {assets} from "../../assets/assets.ts";
import { useDispatch, useSelector } from 'react-redux';
import { fetchCaptcha, login } from '../../features/auth/authSlice.ts';


const Login: React.FC = () => {
    const navigate = useNavigate();
    const dispatch:any = useDispatch();
    const { loadingCaptcha, captcha, isAuthenticated } = useSelector((state: any) => state.auth);
    const { t, i18n } = useTranslation();
    const [identifierType, setIdentifierType] = useState<string>('email');
    const [loading, setLoading] = useState(false);
    const [form] = Form.useForm();

    if(isAuthenticated) {
        navigate('/auth/new-subscription');
    }

    const onFinish = (values: any) => {
        setLoading(true);
        const credentialsData = {
            [identifierType]: values.identifier,
            password: values.password,
            captcha: values.captcha,
            generatedCaptcha: localStorage.getItem("hashed_captcha_text")
        };
        try {
            dispatch(login(credentialsData)).unwrap()
            .then((response: any) => {
                console.log(response);
                setLoading(false);
            }).catch((error: any) => {
                toast.error(error.message);
                setLoading(false);
            });
        } catch (error) {
            toast.error(t("messages.error"));
            setLoading(false);
        }
    };

    const handleTabChange = (key: string) => {
        setIdentifierType(key);
        form.resetFields();
    };

    const tabItems = [
        {
            key: 'email',
            label: (
                <span className="flex items-center gap-2">
                    <Mail size={16} />
                    {t('login.email')}
                </span>
            ),
        },
        {
            key: 'phone',
            label: (
                <span className="flex items-center gap-2">
                    <Phone size={16} />
                    {t('login.phone')}
                </span>
            ),
        },
        {
            key: 'cin',
            label: (
                <span className="flex items-center gap-2">
                    <IdCard size={16} />
                    {t('login.cin')}
                </span>
            ),
        },
    ];

    const reloadCaptcha = async () => {
        await dispatch(fetchCaptcha());
    };

    useEffect(() => {
        reloadCaptcha()
    }, []);

    return (
        <div
            className="min-h-screen flex"
        >
            <ToastContainer />
            {/*|--------------------------------------------------------------------------
            |  - LOGIN FORM
            |-------------------------------------------------------------------------- */}
            <div
                className="
                    w-full
                    md:w-full
                    flex items-center justify-center
                    bg-center bg-no-repeat bg-contain
                    "
                style={{
                    backgroundImage: `url(${assets.bg})`,
                }}
            >
                <Card
                    className="w-full max-w-full  md:max-w-3xl lg:max-w-4xl m-5 !drop-shadow-sm py-20 lg:py-8"
                    bordered={false}
                >
                    {/*|--------------------------------------------------------------------------
                    |  - SELECT LANGUAGES
                    |-------------------------------------------------------------------------- */}
                    <div className={`absolute top-5 ${
                        i18n.language === "ar" ? "right-5" : "left-5"
                    } `}>
                        <LanguageSelector/>
                    </div>

                    <div className="text-center mb-7">
                        <h2 className="text-3xl font-bold text-gray-800 mb-2">{t('login.welcome')}</h2>
                        <p className="text-gray-600 flex items-center justify-center gap-2">
                            {t('login.subtitle')}
                            <Tooltip title={t('login.tooltip')}>
                                <HelpCircle size={16} className="text-gray-400 cursor-help"/>
                            </Tooltip>
                        </p>
                    </div>

                    <div className="flex justify-center mb-6">
                        <Tabs
                            activeKey={identifierType}
                            onChange={handleTabChange}
                            centered
                            items={tabItems}
                        />
                    </div>

                    <Form
                        form={form}
                        name="login"
                        initialValues={{
                            remember: true,
                            countryCode: '+216',
                        }}
                        onFinish={onFinish}
                        layout="vertical"
                        size="large"
                        className="px-2"
                    >
                        <Form.Item
                            hasFeedback
                            name="identifier"
                            rules={[
                                {required: true, message: t('login.validation.required')},
                                ...(identifierType === 'email'
                                    ? [
                                        {
                                            type: 'email' as const,
                                            message: t('login.validation.email'),
                                        },
                                    ]
                                    : []),
                                ...(identifierType === 'phone'
                                    ? [
                                        {
                                            pattern: /^[0-9]{8}$/,
                                            message: t('login.validation.phone'),
                                        },
                                    ]
                                    : []),
                                ...(identifierType === 'cin'
                                    ? [
                                        {
                                            pattern: /^[0-9]{8}$/,
                                            message: t('login.validation.cin'),
                                        },
                                    ]
                                    : []),
                            ]}
                        >
                            {
                                identifierType === 'email' ? (
                                    <Input
                                        type="email"
                                        placeholder={t(`login.placeholder.email`)}
                                        className="text-sm py-3"
                                    />
                                ) :  identifierType === 'phone' ? (
                                    <Space.Compact style={{width: '100%'}}>
                                        <Form.Item hasFeedback={false} name="countryCode" noStyle>
                                            <Select
                                                disabled={true}
                                                suffixIcon={null}
                                                virtual={false}
                                                className="!w-1/2 !h-12"
                                                options={countryOptions.map((country:any) => ({
                                                    value: country.value,
                                                    label: (
                                                        <div className="flex items-center gap-2">
                                                            <img
                                                                src={country.flag}
                                                                alt="."
                                                                className="w-5 h-3 rounded-sm"
                                                            />
                                                            <span>({country.value})</span>
                                                        </div>
                                                    ),
                                                }))}
                                            />
                                        </Form.Item>

                                        <Input
                                            className="text-sm py-3"
                                            maxLength={8}
                                            minLength={8}
                                            type="number"
                                            placeholder={t(`login.placeholder.phone`)}
                                        />
                                    </Space.Compact>

                                ) : (
                                    <Input
                                        maxLength={8}
                                        minLength={8}
                                        type="number"
                                        placeholder={t('login.placeholder.cin')}
                                        className="text-sm py-3"
                                    />
                                )
                            }
                        </Form.Item>

                        <Form.Item
                            name="password"
                            rules={[{
                                required: true,
                                message: t('login.validation.required') || 'Password is required'
                            }]}
                        >
                            <Input.Password
                                prefix={<Lock className="text-gray-400" size={18}/>}
                                placeholder={t('login.placeholder.password') || 'Enter your password'}
                                className="text-sm py-3"
                            />
                        </Form.Item>
                        <Form.Item
                            name="captcha" className="!mb-3"
                            rules={[{
                                required: true, message:""
                            }]}
                        >
                            <div className="p-4 rounded-xl border border-gray-200">
                                {loadingCaptcha ? (
                                    <Spin className="my-3 mx-2"  indicator={<Loader className="animate-spin"/>}/>
                                ) : (
                                    <div className="flex items-center gap-1 mb-4">
                                        <img
                                            src={captcha}
                                            className="h-16 w-48 rounded border-2 border-white shadow-sm"
                                        />
                                        <Button
                                            className="hover:bg-gray-200 "
                                            icon={<RefreshCcw size={20} className="text-gray-600"/>}
                                            onClick={reloadCaptcha}
                                        />
                                    </div>
                                )}
                                <Input
                                    className="text-sm py-3"
                                    placeholder={t('login.placeholder.captcha')}
                                />
                            </div>
                        </Form.Item>

                        <Form.Item className="mb-2">
                            <Button
                                type="primary"
                                loading={loading}
                                htmlType="submit"
                                className="w-full h-12 text-md font-medium shadow-md hover:shadow-lg transition-all duration-300"
                            >
                                {t('login.signIn') || 'Sign In'}
                            </Button>
                        </Form.Item>

                        <div className="text-center space-y-4">
                            <Link to="/reset-password" className="text-red-600 hover:text-red-500 hover:underline transition-colors">
                                {t('login.forgotPassword')}
                            </Link>
                            <Divider className="border-gray-200">
                                <span className="text-gray-400 text-sm">{t('login.or') || 'OR'}</span>
                            </Divider>
                            <div className="text-gray-600">
                                {t('login.noAccount')}
                                <Link to='/register' className="ml-1 text-red-600 hover:text-red-500 hover:underline transition-colors font-medium">
                                    {t('login.signUp')}
                                </Link>
                            </div>
                        </div>
                    </Form>
                </Card>
            </div>

            {/* Right side - Image */}
            {/* <LandingSide/> */}
        </div>
    );
};

export default Login;