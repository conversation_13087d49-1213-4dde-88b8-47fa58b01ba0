import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import api from "../../config/axiosConfig.tsx";

const URL = '/periodicities';

const initialState = {
    items: [],
    paginatedItems: null,
    loading: false,
    error: null,
    currentItem: null
};

export const getPeriodicityAll: any = createAsyncThunk(
    "getPeriodicityAll",
    async (_:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}-all`;
            const resp:any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getPeriodicity: any = createAsyncThunk(
    "getPeriodicity",
    async (
        data: {
            pageNumber: number;
            perPage: number;
            params:any,
            sort:any,
            filter:any
        },
        thunkAPI:any
    ) => {
        try {
            const { nom_fr, nom_en, nom_ar, max_days_per_week } = data.params;
            const sort = data.sort;

            let url = `${URL}?page=${data.pageNumber}&perPage=${data.perPage}`;
            const searchParams = [];

            if (nom_fr) {
                searchParams.push(`nom_fr:${nom_fr}`);
            }
            if (nom_en) {
                searchParams.push(`nom_en:${nom_en}`);
            }
            if (nom_ar) {
                searchParams.push(`nom_ar:${nom_ar}`);
            }
            if (max_days_per_week) {
                searchParams.push(`max_days_per_week:${max_days_per_week}`);
            }

            if (searchParams.length > 0) {
                url += `&search=${searchParams.join(';')}`;
            }

            const orderBy = [];
            const sortedBy = [];

            for (const [field, order] of Object.entries(sort)) {
                orderBy.push(field);
                sortedBy.push(order === 'ascend' ? 'asc' : 'desc');
            }

            if (orderBy.length > 0) {
                url += `&orderBy=${orderBy.join(',')}&sortedBy=${sortedBy.join(',')}`;
            }
            const joint = "&searchJoin=and";
            url += joint;
            const resp:any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const storePeriodicity: any = createAsyncThunk(
    "storePeriodicity",
    async (data:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}`;
            const resp:any = await api.post(url, data);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const updatePeriodicity: any = createAsyncThunk(
    "updatePeriodicity",
    async (data: any, thunkAPI:any) => {
        try {
            const { id, ...payload } = data;
            const url:string = `${URL}/${id}`;
            const resp:any = await api.put(url, payload);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const deletePeriodicity: any = createAsyncThunk(
    "deletePeriodicity",
    async (id:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}/${id}`;
            const resp:any = await api.delete(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data.message);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

const periodicitySlice = createSlice({
    name: 'periodicity',
    initialState,
    reducers: {
        setCurrentItem: (state, action) => {
            state.currentItem = action.payload;
        },
        clearError: (state) => {
            state.error = null;
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(getPeriodicityAll.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getPeriodicityAll.fulfilled, (state, action) => {
                state.loading = false;
                state.items = action.payload;
            })
            .addCase(getPeriodicityAll.rejected, (state:any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            });
    }
});

export const { setCurrentItem, clearError } = periodicitySlice.actions;
export default periodicitySlice.reducer;