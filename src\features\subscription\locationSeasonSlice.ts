import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import api from "../../config/axiosConfig.tsx";

const URL = "/location-seasons";

const initialState = {
    items: [],
    paginatedItems: null,
    loading: false,
    error: null,
    currentItem: null
};

export const getLocationSeasonsAll: any = createAsyncThunk(
    "getLocationSeasonsAll",
    async (_:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}-all`;
            const resp:any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getActiveLocationSeasons: any = createAsyncThunk(
    "getActiveLocationSeasons",
    async (_:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}/active`;
            const resp:any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getLocationSeasons: any = createAsyncThunk(
    "getLocationSeasons",
    async (
        data: {
            pageNumber: number;
            perPage: number;
            params: any;
            sort: any;
            filter: any;
        },
        thunkAPI: any
    ) => {
        try {
            const { nom_fr, nom_en, nom_ar, start_date, end_date, status } = data.params;
            const sort = data.sort;

            let url = `${URL}?page=${data.pageNumber}&perPage=${data.perPage}`;
            const searchParams = [];

            if (nom_fr) {
                searchParams.push(`nom_fr:${nom_fr}`);
            }
            if (nom_en) {
                searchParams.push(`nom_en:${nom_en}`);
            }
            if (nom_ar) {
                searchParams.push(`nom_ar:${nom_ar}`);
            }
            if (start_date) {
                searchParams.push(`start_date:${start_date}`);
            }
            if (end_date) {
                searchParams.push(`end_date:${end_date}`);
            }
            if (status !== undefined) {
                searchParams.push(`status:${status}`);
            }

            if (searchParams.length > 0) {
                url += `&search=${searchParams.join(';')}`;
            }

            const orderBy = [];
            const sortedBy = [];

            for (const [field, order] of Object.entries(sort)) {
                orderBy.push(field);
                sortedBy.push(order === 'ascend' ? 'asc' : 'desc');
            }

            if (orderBy.length > 0) {
                url += `&orderBy=${orderBy.join(',')}&sortedBy=${sortedBy.join(',')}`;
            }
            const joint = '&searchJoin=and';
            url += joint;

            const resp = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getLocationSeasonById: any = createAsyncThunk(
    "getLocationSeasonById",
    async (id: number, thunkAPI: any) => {
        try {
            const url: string = `${URL}/${id}`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const storeLocationSeason: any = createAsyncThunk(
    "storeLocationSeason",
    async (data: any, thunkAPI: any) => {
        try {
            const url: string = `${URL}`;
            const resp: any = await api.post(url, data);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const updateLocationSeason: any = createAsyncThunk(
    "updateLocationSeason",
    async (data: any, thunkAPI: any) => {
        try {
            const { id, ...payload } = data;
            const url: string = `${URL}/${id}`;
            const resp: any = await api.put(url, payload);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const deleteLocationSeason: any = createAsyncThunk(
    "deleteLocationSeason",
    async (id: number, thunkAPI: any) => {
        try {
            const url: string = `${URL}/${id}`;
            const resp: any = await api.delete(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

const locationSeasonSlice = createSlice({
    name: 'locationSeason',
    initialState,
    reducers: {
        setCurrentItem: (state, action) => {
            state.currentItem = action.payload;
        },
        clearError: (state) => {
            state.error = null;
        }
    },
    extraReducers: (builder) => {
        builder
            // getLocationSeasonsAll
            .addCase(getLocationSeasonsAll.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getLocationSeasonsAll.fulfilled, (state, action) => {
                state.loading = false;
                state.items = action.payload;
            })
            .addCase(getLocationSeasonsAll.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            // getActiveLocationSeasons
            .addCase(getActiveLocationSeasons.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getActiveLocationSeasons.fulfilled, (state, action) => {
                state.loading = false;
                state.items = action.payload;
            })
            .addCase(getActiveLocationSeasons.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            // getLocationSeasons
            .addCase(getLocationSeasons.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getLocationSeasons.fulfilled, (state, action) => {
                state.loading = false;
                state.paginatedItems = action.payload;
            })
            .addCase(getLocationSeasons.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            // getLocationSeasonById
            .addCase(getLocationSeasonById.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getLocationSeasonById.fulfilled, (state, action) => {
                state.loading = false;
                state.currentItem = action.payload;
            })
            .addCase(getLocationSeasonById.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            // storeLocationSeason
            .addCase(storeLocationSeason.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(storeLocationSeason.fulfilled, (state:any, action) => {
                state.loading = false;
                if (state.items) {
                    state.items.push(action.payload);
                }
            })
            .addCase(storeLocationSeason.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            // updateLocationSeason
            .addCase(updateLocationSeason.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(updateLocationSeason.fulfilled, (state:any, action) => {
                state.loading = false;
                if (state.items) {
                    const index = state.items.findIndex((item:any) => item.id === action.payload.id);
                    if (index !== -1) {
                        state.items[index] = action.payload;
                    }
                }
            })
            .addCase(updateLocationSeason.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            // deleteLocationSeason
            .addCase(deleteLocationSeason.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(deleteLocationSeason.fulfilled, (state:any, action) => {
                state.loading = false;
                if (state.items) {
                    state.items = state.items.filter((item:any) => item.id !== action.payload);
                }
            })
            .addCase(deleteLocationSeason.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            });
    }
});

export const { setCurrentItem, clearError } = locationSeasonSlice.actions;
export default locationSeasonSlice.reducer;