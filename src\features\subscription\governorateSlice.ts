import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import api from "../../config/axiosConfig.tsx";

const initialState = {
    items: [],
    paginatedItems: null,
    loading: false,
    error : null,
    currentItem: null
};

const URL = '/governorates';

export const getGovernorateAll = createAsyncThunk(
    "getGovernorateAll",
    async (_, thunkAPI) => {
        try {
            const url = `${URL}-all`;
            const resp = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            }
            return thunkAPI.rejectWithValue("An unexpected error occurred.");
        }
    }
);

export const getGovernorates = createAsyncThunk(
    "getGovernorates",
    async (data: any, thunkAPI) => {
        try {
            const { nom_fr, nom_en, nom_ar } = data.params;
            const sort = data.sort;

            let url = `${URL}?page=${data.pageNumber}&perPage=${data.perPage}`;
            const searchParams = [];

            if (nom_fr) searchParams.push(`nom_fr:${nom_fr}`);
            if (nom_en) searchParams.push(`nom_en:${nom_en}`);
            if (nom_ar) searchParams.push(`nom_ar:${nom_ar}`);

            if (searchParams.length > 0) {
                url += `&search=${searchParams.join(';')}`;
            }

            const orderBy = [];
            const sortedBy = [];

            for (const [field, order] of Object.entries(sort)) {
                orderBy.push(field);
                sortedBy.push(order === 'ascend' ? 'asc' : 'desc');
            }

            if (orderBy.length > 0) {
                url += `&orderBy=${orderBy.join(',')}&sortedBy=${sortedBy.join(',')}`;
            }
            const joint = "&searchJoin=and";
            url += joint;
            const resp = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            }
            return thunkAPI.rejectWithValue("An unexpected error occurred.");
        }
    }
);

export const storeGovernorate = createAsyncThunk(
    "storeGovernorate",
    async (data: any, thunkAPI) => {
        try {
            const resp = await api.post(URL, data);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            }
            return thunkAPI.rejectWithValue("An unexpected error occurred.");
        }
    }
);

export const updateGovernorate = createAsyncThunk(
    "updateGovernorate",
    async (data: any, thunkAPI) => {
        try {
            const { id, ...payload } = data;
            const resp = await api.put(`${URL}/${id}`, payload);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            }
            return thunkAPI.rejectWithValue("An unexpected error occurred.");
        }
    }
);

export const deleteGovernorate = createAsyncThunk(
    "deleteGovernorate",
    async (id: number, thunkAPI) => {
        try {
            await api.delete(`${URL}/${id}`);
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data.message);
            }
            return thunkAPI.rejectWithValue("An unexpected error occurred.");
        }
    }
);

const governorateSlice = createSlice({
    name: 'governorate',
    initialState,
    reducers: {
        setCurrentItem: (state, action) => {
            state.currentItem = action.payload;
        },
        clearError: (state) => {
            state.error = null;
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(getGovernorateAll.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getGovernorateAll.fulfilled, (state, action) => {
                state.loading = false;
                state.items = action.payload;
            })
            .addCase(getGovernorateAll.rejected, (state:any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            .addCase(getGovernorates.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getGovernorates.fulfilled, (state, action) => {
                state.loading = false;
                state.paginatedItems = action.payload;
            })
            .addCase(getGovernorates.rejected, (state:any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            .addCase(storeGovernorate.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(storeGovernorate.fulfilled, (state:any, action:any) => {
                state.loading = false;
                state.items.push(action.payload);
            })
            .addCase(storeGovernorate.rejected, (state:any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            .addCase(updateGovernorate.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(updateGovernorate.fulfilled, (state:any, action:any) => {
                state.loading = false;
                const index = state.items.findIndex((item:any) => item.id === action.payload.id);
                if (index !== -1) {
                    state.items[index] = action.payload;
                }
            })
            .addCase(updateGovernorate.rejected, (state:any, action:any) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            .addCase(deleteGovernorate.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(deleteGovernorate.fulfilled, (state) => {
                state.loading = false;
            })
            .addCase(deleteGovernorate.rejected, (state:any, action:any) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            });
    }
});

export const { setCurrentItem, clearError } = governorateSlice.actions;
export default governorateSlice.reducer;

