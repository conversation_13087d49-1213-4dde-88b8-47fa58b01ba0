import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Card, Row, Col, Typography, Divider, Button, Spin, Alert } from 'antd';
import { useTranslation } from 'react-i18next';
import { CheckCircleIcon, XCircleIcon, PrinterIcon, ArrowLeftIcon, IdCardIcon, CalendarIcon, UserIcon } from 'lucide-react';
import dayjs from 'dayjs';
import { assets } from '../../assets/assets';

const { Title, Text } = Typography;

const QrCodeDataView: React.FC = () => {
  const { t } = useTranslation();
  // const currentLang = i18n.language;
  const location = useLocation();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [subscriptionData, setSubscriptionData] = useState<any>(null);

  useEffect(() => {
    // Extraire les données du QR code depuis l'URL
    const searchParams = new URLSearchParams(location.search);
    const encodedData = searchParams.get('data');

    if (!encodedData) {
      setError(t('qrcode.noData', 'Aucune donnée trouvée'));
      setLoading(false);
      return;
    }

    try {
      // Décoder les données
      const decodedData = JSON.parse(decodeURIComponent(encodedData));
      setSubscriptionData(decodedData);
      setLoading(false);
    } catch (err) {
      console.error('Error parsing QR code data:', err);
      setError(t('qrcode.invalidData', 'Données invalides'));
      setLoading(false);
    }
  }, [location.search, t]);

  const formatDate = (dateString: string) => {
    if (!dateString) return '-';
    
    // Si le format est YYYYMMDD, le convertir en YYYY-MM-DD
    if (dateString.length === 8 && !dateString.includes('-')) {
      const year = dateString.substring(0, 4);
      const month = dateString.substring(4, 6);
      const day = dateString.substring(6, 8);
      dateString = `${year}-${month}-${day}`;
    }
    
    return dayjs(dateString).format('DD/MM/YYYY');
  };

  const handlePrint = () => {
    window.print();
  };

  const handleBack = () => {
    navigate(-1);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Spin size="large" tip={t('common.loading', 'Chargement...')} />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <Alert
          message={t('qrcode.error', 'Erreur')}
          description={error}
          type="error"
          showIcon
          className="mb-4"
        />
        <Button 
          type="primary" 
          onClick={handleBack}
          icon={<ArrowLeftIcon className="w-4 h-4 mr-2" />}
        >
          {t('common.back', 'Retour')}
        </Button>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 min-h-screen p-4 md:p-8 print:p-0 print:bg-white">
      <div className="max-w-4xl mx-auto">
        {/* Actions non imprimables */}
        <div className="flex justify-between mb-6 print:hidden">
          <Button 
            onClick={handleBack}
            icon={<ArrowLeftIcon className="w-4 h-4 mr-2" />}
          >
            {t('common.back', 'Retour')}
          </Button>
          <Button 
            type="primary" 
            onClick={handlePrint}
            icon={<PrinterIcon className="w-4 h-4 mr-2" />}
          >
            {t('common.print', 'Imprimer')}
          </Button>
        </div>

        {/* Carte principale */}
        <Card className="shadow-md print:shadow-none">
          {/* En-tête */}
          <div className="flex items-center justify-between border-b border-gray-200 pb-4 mb-6">
            <div className="flex items-center">
              <img src={assets.logo} alt="SRTGN Logo" className="h-16 mr-4" />
              <div>
                <Title level={4} className="m-0">{t('company.name', 'الشركة الجهوية للنقل لولاية نابل')}</Title>
                <Text className="text-gray-500">{t('subscription.verification', 'التحقق من الاشتراك')}</Text>
              </div>
            </div>
            <div className="flex items-center">
              {subscriptionData?.id ? (
                <div className="flex items-center bg-green-50 text-green-700 px-3 py-1 rounded-full border border-green-200">
                  <CheckCircleIcon className="w-5 h-5 mr-2" />
                  <span className="font-medium">{t('subscription.valid', 'اشتراك صالح')}</span>
                </div>
              ) : (
                <div className="flex items-center bg-red-50 text-red-700 px-3 py-1 rounded-full border border-red-200">
                  <XCircleIcon className="w-5 h-5 mr-2" />
                  <span className="font-medium">{t('subscription.invalid', 'اشتراك غير صالح')}</span>
                </div>
              )}
            </div>
          </div>

          {/* Informations de l'abonnement */}
          <Row gutter={[24, 24]}>
            {/* Informations principales */}
            <Col xs={24} md={12}>
              <Card 
                title={
                  <div className="flex items-center">
                    <IdCardIcon className="w-5 h-5 mr-2 text-primary-color" />
                    <span>{t('subscription.mainInfo', 'معلومات الاشتراك')}</span>
                  </div>
                } 
                className="h-full"
                bordered={false}
                headStyle={{ borderBottom: '2px solid #f0f0f0' }}
              >
                <div className="space-y-4">
                  <div>
                    <Text type="secondary">{t('subscription.subscriptionId', 'رقم الاشتراك')}</Text>
                    <div className="font-semibold text-lg">{subscriptionData?.id || '-'}</div>
                  </div>
                  <div>
                    <Text type="secondary">{t('subscription.paymentRef', 'مرجع الخلاص')}</Text>
                    <div className="font-semibold text-lg">{subscriptionData?.ref || '-'}</div>
                  </div>
                  <div>
                    <Text type="secondary">{t('subscription.amount', 'المبلغ')}</Text>
                    <div className="font-semibold text-lg text-green-700">
                      {subscriptionData?.amount ? `${subscriptionData.amount} TND` : '-'}
                    </div>
                  </div>
                </div>
              </Card>
            </Col>

            {/* Informations du client */}
            <Col xs={24} md={12}>
              <Card 
                title={
                  <div className="flex items-center">
                    <UserIcon className="w-5 h-5 mr-2 text-primary-color" />
                    <span>{t('subscription.clientInfo', 'معلومات المشترك')}</span>
                  </div>
                } 
                className="h-full"
                bordered={false}
                headStyle={{ borderBottom: '2px solid #f0f0f0' }}
              >
                <div className="space-y-4">
                  <div>
                    <Text type="secondary">{t('subscription.fullName', 'الإسم واللقب')}</Text>
                    <div className="font-semibold text-lg">{subscriptionData?.name || '-'}</div>
                  </div>
                  <div>
                    <Text type="secondary">{t('subscription.cin', 'ر.ب.ت.و')}</Text>
                    <div className="font-semibold text-lg">{subscriptionData?.cin || '-'}</div>
                  </div>
                  <div>
                    <Text type="secondary">{t('subscription.subscriptionType', 'نوع الاشتراك')}</Text>
                    <div className="font-semibold text-lg">{subscriptionData?.type || '-'}</div>
                  </div>
                </div>
              </Card>
            </Col>

            {/* Date et validité */}
            <Col xs={24}>
              <Card 
                title={
                  <div className="flex items-center">
                    <CalendarIcon className="w-5 h-5 mr-2 text-primary-color" />
                    <span>{t('subscription.dateAndValidity', 'التاريخ والصلاحية')}</span>
                  </div>
                } 
                bordered={false}
                headStyle={{ borderBottom: '2px solid #f0f0f0' }}
              >
                <Row gutter={24}>
                  <Col xs={24} md={12}>
                    <div className="space-y-4">
                      <div>
                        <Text type="secondary">{t('subscription.paymentDate', 'تاريخ الخلاص')}</Text>
                        <div className="font-semibold text-lg">{formatDate(subscriptionData?.date) || '-'}</div>
                      </div>
                    </div>
                  </Col>
                  <Col xs={24} md={12}>
                    <div className="space-y-4">
                      <div>
                        <Text type="secondary">{t('subscription.verificationDate', 'تاريخ التحقق')}</Text>
                        <div className="font-semibold text-lg">{dayjs().format('DD/MM/YYYY HH:mm')}</div>
                      </div>
                    </div>
                  </Col>
                </Row>
              </Card>
            </Col>
          </Row>

          {/* Pied de page */}
          <Divider className="my-6" />
          <div className="text-center text-gray-500">
            <div className="mb-2">{t('company.name', 'الشركة الجهوية للنقل لولاية نابل')}</div>
            <div className="text-xs">{t('subscription.verificationInfo', 'تم التحقق من صحة هذا الاشتراك إلكترونيًا')}</div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default QrCodeDataView;
