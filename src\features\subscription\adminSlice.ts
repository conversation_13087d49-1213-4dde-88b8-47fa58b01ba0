import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import api from "../../config/axiosConfig.tsx";

const URL = "/admins";

const initialState = {
    items: [],
    paginatedItems: null,
    loading: false,
    error: null,
    currentItem: null
};

export const getAdminsAll: any = createAsyncThunk(
  "getAdminsAll",
  async (_: any, thunkAPI: any) => {
    try {
      const url: string = `${URL}-all`;
      const resp: any = await api.get(url);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

export const getAdmins: any = createAsyncThunk(
  "getAdmins",
  async (
    data: {
      pageNumber: number;
      perPage: number;
      params: any;
      sort: any;
      filter: any;
    },
    thunkAPI: any
  ) => {
    try {
      const { email, phone, cin } = data.params;
      const sort: any = data.sort;

      let url: any = `${URL}?page=${data.pageNumber}&perPage=${data.perPage}`;
      const searchParams: any = [];

      if (email) {
        searchParams.push(`email:${email}`);
      }
      if (cin) {
        searchParams.push(`cin:${cin}`);
      }
      if (phone) {
        searchParams.push(`phone:${phone}`);
      }
      if (searchParams.length > 0) {
        url += `&search=${searchParams.join(";")}`;
      }

      const orderBy: any = [];
      const sortedBy: any = [];

      for (const [field, order] of Object.entries(sort)) {
        orderBy.push(field);
        sortedBy.push(order === "ascend" ? "asc" : "desc");
      }

      if (orderBy.length > 0) {
        url += `&orderBy=${orderBy.join(",")}&sortedBy=${sortedBy.join(",")}`;
      }
      const joint = "&searchJoin=and";
      url += joint;
      const resp: any = await api.get(url);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

export const storeAdmin: any = createAsyncThunk(
  "storeAdmin",
  async (data: any, thunkAPI: any) => {
    try {
      const url: string = `${URL}`;
      const resp: any = await api.post(url, data);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data);
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

export const updateAdmin: any = createAsyncThunk(
  "updateAdmin",
  async (data: any, thunkAPI: any) => {
    try {
      const { id, ...payload } = data;
      const url: string = `${URL}/${id}`;
      const resp: any = await api.put(url, payload);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data);
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

export const deleteAdmin: any = createAsyncThunk(
  "deleteAdmin",
  async (id: any, thunkAPI: any) => {
    try {
      const url: string = `${URL}/${id}`;
      const resp: any = await api.delete(url);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data.message);
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

export const forgetPassword: any = createAsyncThunk(
  "forgetPassword",
  async (email: any, thunkAPI: any) => {
    try {
      const url: string = `/forget-password`;
      const resp: any = await api.post(url, { email });
      return resp.data;
    } catch (error: any) {
      console.log(error);
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data);
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);
export const verifCode: any = createAsyncThunk(
  "verifcoderesetPassword",
  async (payload: { email: string; token: string }, thunkAPI: any) => {
    try {
      const url: string = `/verif-code-password`;
      const resp: any = await api.post(url, payload);
      return resp.data;
    } catch (error: any) {
        console.log(error);
        if (error.response) {
          const { status, data } = error.response;
          if (status === 403) {
            window.location.href = "/unauthorized";
          }
          return thunkAPI.rejectWithValue(data);
        } else {
          return thunkAPI.rejectWithValue("An unexpected error occurred.");
        }
    }
  }
);
export const changePassword: any = createAsyncThunk(
  "resetPassword",
  async (
    payload: { email: string; password: string; password_confirmation: string },
    thunkAPI: any
  ) => {
    try {
      const url: string = `/reset-password`;
      const resp: any = await api.post(url, payload);
      return resp.data;
    } catch (error: any) {
      console.log(error);
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data);
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

const adminSlice = createSlice({
    name: 'admin',
    initialState,
    reducers: {
        setCurrentItem: (state, action) => {
            state.currentItem = action.payload;
        },
        clearError: (state) => {
            state.error = null;
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(getAdminsAll.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getAdminsAll.fulfilled, (state, action) => {
                state.loading = false;
                state.items = action.payload;
            })
            .addCase(getAdminsAll.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            .addCase(getAdmins.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getAdmins.fulfilled, (state, action) => {
                state.loading = false;
                state.paginatedItems = action.payload;
            })
            .addCase(getAdmins.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            .addCase(storeAdmin.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(storeAdmin.fulfilled, (state, action) => {
                state.loading = false;
            })
            .addCase(storeAdmin.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            .addCase(updateAdmin.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(updateAdmin.fulfilled, (state, action) => {
                state.loading = false;
            })
            .addCase(updateAdmin.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
            .addCase(deleteAdmin.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(deleteAdmin.fulfilled, (state, action) => {
                state.loading = false;
            })
            .addCase(deleteAdmin.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            });
    }
});

export const { setCurrentItem, clearError } = adminSlice.actions;
export default adminSlice.reducer;
