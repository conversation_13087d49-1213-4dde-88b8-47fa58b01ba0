import { <PERSON><PERSON>, <PERSON><PERSON> } from "antd";
import { useTranslation } from "react-i18next";
import { useRef } from "react";
import { PrinterIcon, ScissorsLineDashed } from "lucide-react";
import { useReactToPrint } from "react-to-print";
import dayjs from "dayjs";
import { assets } from "../../assets/assets";
import { QRCodeSVG } from "qrcode.react";
import "./subsReceipt.css"

const SubsReceipt: any = ({
    isVisible,
    onClose,
    abnRecord,
}: any) => {
    const { t, i18n } = useTranslation();
    const currentLang = i18n.language;
    const printRef = useRef<HTMLDivElement>(null);

    const handlePrint = useReactToPrint({
        contentRef: printRef,
        documentTitle: "",
        pageStyle: `
            @page {
                size: A4;
            }
        `,
        onAfterPrint: () => console.log('Print completed successfully')
    });

    const formatDate = (date: string | undefined) => {
        if (!date) return "";
        return dayjs(date).format("YYYY-MM-DD");
    };

    const generateQRCodeData = () => {
        if (!abnRecord) return "";

        // Simplifier les données pour améliorer la lisibilité du QR code
        // Inclure uniquement les informations essentielles
        const qrData = {
            id: abnRecord.id || "",
            ref: abnRecord.latestTransaction?.transaction_reference || "",
            cin: abnRecord.client?.identity_number || "",
            name: abnRecord.client ? `${abnRecord.client.firstname || ""} ${abnRecord.client.lastname || ""}` : "",
            type: abnRecord.subs_type?.[`nom_${currentLang}`] || "",
            date: abnRecord.latestTransaction?.payment_date ? dayjs(abnRecord.latestTransaction.payment_date).format("YYYYMMDD") : "",
            amount: abnRecord.latestTransaction?.amount || ""
        };

        // Encoder les données pour l'URL
        const encodedData = encodeURIComponent(JSON.stringify(qrData));

        // Générer l'URL complète pour la vérification
        const baseUrl = window.location.origin;
        return `${baseUrl}/subscription-verify?data=${encodedData}`;
    };

    return (
        <Modal
            width={900}
            title={false}
            open={isVisible}
            onCancel={onClose}
            footer={[
                <Button
                    key="print"
                    type="primary"
                    onClick={handlePrint}
                    icon={<PrinterIcon className="w-4 h-4 mr-1" />}
                >
                    {t("common.print", "Print")}
                </Button>,
                <Button key="close" onClick={onClose}>
                    {t("common.close", "Close")}
                </Button>
            ]}
        >
            
           <div ref={printRef}>
             <div className="receipt-container2 subs-receipt-component2" dir="rtl" style={{height: "100vh"}}>
                <div className="flex flex-col justify-between h-full">
                    <div className="content-docuement">
                        <div className="header flex items-center justify-center">
                            <img
                                className="h-[60px]"
                                src={assets.logo}
                                alt="Logo"
                            />
                            <div className="pt-1">
                                <div className="company-name">الشركة الجهوية للنقل لولاية نابل</div>
                                <div className="payment-method">الدفع البريدي الرقمي</div>
                            </div>
                        </div>
                        <div className="date text-left">التاريخ: {formatDate(abnRecord?.latestTransaction?.payment_date) || "...................."}</div>
                        <div className="section-title text-center">مطلب للحصول على إشتراك نقل {abnRecord?.subs_type?.is_student ? "جامعي" : "مدني"}</div>
                        <div className="section-title">بطاقة إرشادات خاصة بالمشترك</div>
                        <div className="flex flex-wrap mt-3 cart-info">
                            <div className="w-1/2">
                                <div className="mb-1"><span className="font-semibold">الإسم واللقب:</span> {abnRecord?.client ? `${abnRecord.client.firstname} ${abnRecord.client.lastname || ""}` : "...................."}</div>
                                <div className="mb-1"><span className="font-semibold">الإسم واقب الولي:</span> {abnRecord?.client?.parent_name || "...................."}</div>
                                <div className="mb-1"><span className="font-semibold">منطقة المؤسسة:</span> {abnRecord?.client?.establishment_zone || "...................."}</div>
                                <div className="mb-1"><span className="font-semibold">الصفة:</span> {abnRecord?.subs_type?.is_student ? "طالب" : "مدني"}</div>
                                <div className="mb-1"><span className="font-semibold">المستوى التعليمي:</span> {abnRecord?.client?.degree?.[`nom_${currentLang}`] || "...................."}</div>
                                <div className="mb-1"><span className="font-semibold">الخط المطلوب:</span> {abnRecord?.line?.[`nom_${currentLang}`] || "...................."}</div>
                            </div>
                            <div className="w-1/2">
                                <div className="mb-1"><span className="font-semibold">تاريخ الولادة:</span> {formatDate(abnRecord?.client?.dob) || "...................."}</div>
                                <div className="mb-1"><span className="font-semibold">ر.ب.ت.و المشترك أو الولي:</span> {abnRecord?.client?.identity_number || "...................."}</div>
                                <div className="mb-1"><span className="font-semibold">المنطقة السكنية:</span> {abnRecord?.client?.residential_zone || "...................."}</div>
                                <div className="mb-1"><span className="font-semibold">المؤسسة التعليمية:</span> {abnRecord?.client?.establishment?.[`nom_${currentLang}`] || "...................."}</div>
                                <div className="mb-1"><span className="font-semibold">القسم:</span> {abnRecord?.client?.class || "...................."}</div>
                            </div>
                        </div>
                        <div className="date mb-6 mt-3 text-left pl-[60px]">الإمضاء</div>
                        <br />
                        <div className="relative my-6 flex items-center">
                            <div className="flex-grow border-t border-gray-300 border-dashed"></div>
                            <div className="mx-4">
                                <ScissorsLineDashed className="w-6 h-6 text-gray-500 transform scale-x-[-1]" />
                            </div>
                            <div className="flex-grow border-t border-gray-300 border-dashed"></div>
                        </div>
                        <div className="section-title"> شهادة حضور يتم تعميرها من المؤسسة التعليمية</div>
                        <div className="flex flex-wrap">
                            <div className="w-3/4">
                                <div className="mb-3">
                                    <p>يشهد مدير {"............................................................ "}</p>
                                    <p>أن {".........................................................."}
                                    الموودة في {"........................................."}</p>
                                    <p>مرسم بمؤسستي للموسم الدراسي {"........................."}</p>
                                    <p>بالقسم {"..............."}</p>
                                </div>
                            </div>
                            <div className="w-1/4">
                                {abnRecord?.client?.photo ? (
                                    <img className="border p-2 h-[140px] w-auto object-cover" src={abnRecord.client.photo} alt="Student" />
                                ) : (
                                    <img className="border p-2 h-[140px]" src={assets.unnamed} alt="Default user" />
                                )}
                            </div>
                        </div>
                        <div className="flex flex-wrap mt-3">
                            <p className="w-full">سلمت هذه الشهادة للمعني بالأمر لإستخراج إشتراك {abnRecord?.subs_type?.is_student ? "مدرسي" : "مدني"}.</p>
                            <div className="w-1/2">
                                <div className="mb-1"><span className="font-semibold">التاريخ:</span> {"...................."}</div>
                            </div>
                            <div className="w-1/2">
                                <div className="date text-left mb-6 mt-3 pl-[60px]">الإمضاء</div>
                            </div>
                        </div>
                    </div>
                    <div className="mt-4 pt-3 border-t border-gray-300 text-center">
                        <p className="text-gray-500">الشركة الجهوية للنقل لولاية نابل</p>
                    </div>
                </div>
            </div>
            
            
            <div className="receipt-container subs-receipt-component" dir="rtl">
                <div className="flex flex-col justify-between h-[95vh]">
                    <div className="content-document">
                        {/* Header */}
                        <div className="header flex items-center justify-center pb-2.5 mb-5">
                            <img
                                className="h-[60px]"
                                src={assets.logo}
                                alt="Logo"
                            />
                            <div className="pt-1 mr-2">
                                <div className="text-base font-bold">{t("company.name", "الشركة الجهوية للنقل لولاية نابل")}</div>
                                <div className="text-base mb-2.5">{t("payment.digital", "الدفع البريدي الرقمي")}</div>
                            </div>
                        </div>
                        <div className="text-left mb-5">{t("common.date", "التاريخ")}: {formatDate(abnRecord?.latestTransaction?.payment_date) || "................."}</div>

                        <div className="font-bold my-2.5">{t("subscription.subscriberInfo", "بطاقة إرشادات خاصة بالمشترك")}</div>
                        <div className="flex flex-wrap">
                            <div className="w-3/4">
                                <div className="mt-5">
                                    <div className="mb-1">
                                        <span className="font-semibold">{t("subscription.paymentRef", "مرجع الخلاص")} :</span>
                                        {abnRecord?.latestTransaction?.transaction_reference || "........................"}
                                    </div>
                                    <div className="mb-1">
                                        <span className="font-semibold">{t("subscription.type", "نوع الإشتراك")} :</span>
                                        {abnRecord?.subs_type?.[`nom_ar`] || "........................"}
                                    </div>
                                    <div className="mb-1">
                                        <span className="font-semibold">{t("subscription.duration", "مدة الإشتراك")} :</span>
                                        {abnRecord?.periodicity?.[`nom_ar`] || "........................"}
                                    </div>
                                    <div className="mb-1">
                                        <span className="font-semibold">{t("subscription.subscriberId", "معرف المشترك")} :</span>
                                        {abnRecord?.client?.id || "........................"}
                                    </div>
                                    <div className="mb-1">
                                        <span className="font-semibold">{t("subscription.subscriptionNumber", "عدد الإشتراك")} :</span>
                                        {abnRecord?.id || "........................"}
                                    </div>
                                </div>
                            </div>
                            <div className="w-1/4">
                                <div className="flex flex-col items-center justify-center">
                                    <div className="bg-white p-4 rounded-md shadow-sm">
                                        <QRCodeSVG
                                            value={generateQRCodeData()}
                                            size={140}
                                            level="M"
                                            bgColor={"#FFFFFF"}
                                            fgColor={"#000000"}
                                            className="qr-code"
                                        />
                                    </div>
                                    <div className="text-center mt-2 text-xs text-gray-500">
                                        {t("subscription.scanQrCode", "امسح رمز QR للتحقق")}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="flex flex-wrap mt-3">
                            <div className="w-1/2">
                                <div className="mb-1">
                                    <span className="font-semibold">{t("subscription.fullName", "الإسم واللقب")}: </span>
                                    {abnRecord?.client ? `${abnRecord.client.firstname} ${abnRecord.client.lastname || ""}` : "....................."}
                                </div>
                                <div className="mb-1">
                                    <span className="font-semibold">{t("subscription.birthDate", "تاريخ الولادة")}:</span>
                                    {formatDate(abnRecord?.client?.dob) || "....................."}
                                </div>
                                <div className="mb-1">
                                    <span className="font-semibold">{t("subscription.phone", "الهاتف")}: </span>
                                    {abnRecord?.client?.phone || "....................."}
                                </div>
                            </div>
                            <div className="w-1/2">
                                <div className="mb-1">
                                    <span className="font-semibold">{t("subscription.cin", "ر.ب.ت.و")} : </span>
                                    {abnRecord?.client?.identity_number || "....................."}
                                </div>
                                {
                                    abnRecord?.subs_type?.is_student && (
                                        <div className="mb-1">
                                            <span className="font-semibold">{t("subscription.academicYear", "السنة الدراسية")}: </span>
                                            {abnRecord?.client?.degree[`nom_${currentLang}`] || "................."}
                                        </div>
                                    )
                                }
                                <div className="mb-1">
                                    <span className="font-semibold">{t("subscription.username", "اسم المستخدم")} :</span>
                                    {abnRecord?.client?.firstname || "...................."} {abnRecord?.client?.lastname || "...................."}
                                </div>
                            </div>
                        </div>

                        <div className="font-bold mt-4 mb-2.5">{t("subscription.paymentDetails", "تفاصيل الدفع")}</div>
                        <table className="w-full border-collapse my-2.5">
                            <thead>
                                <tr>
                                    <th className="border border-gray-200 p-2 text-right">{t("subscription.identification", "التعريف")}</th>
                                    <th className="border border-gray-200 p-2 text-right">{t("subscription.paymentRef", "مرجع الخلاص")}</th>
                                    <th className="border border-gray-200 p-2 text-right">{t("subscription.paymentDate", "تاريخ الخلاص")}</th>
                                    <th className="border border-gray-200 p-2 text-right">{t("subscription.amount", "المبلغ")}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td className="border border-gray-200 p-2 text-right">{abnRecord?.ref || "....................."}</td>
                                    <td className="border border-gray-200 p-2 text-right">{abnRecord?.latestTransaction?.transaction_reference || "....................."}</td>
                                    <td className="border border-gray-200 p-2 text-right">{formatDate(abnRecord?.latestTransaction?.payment_date) || "....................."}</td>
                                    <td className="border border-gray-200 p-2 text-right">{abnRecord?.latestTransaction?.amount ? `${abnRecord.latestTransaction.amount} TND` : "....................."}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    {/* Footer */}
                    <div className="mt-4 pt-3 border-t border-gray-200 text-center">
                        <p className="text-gray-500">{t("company.name", "الشركة الجهوية للنقل لولاية نابل")}</p>
                    </div>
                </div>
            </div>
           </div>
        </Modal>
    );
}

export default SubsReceipt;