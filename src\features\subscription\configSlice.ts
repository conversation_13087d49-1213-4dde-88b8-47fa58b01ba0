import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import api from "../../config/axiosConfig.tsx";

const URL = '/configs';

const initialState = {
    items: [],
    paginatedItems: null,
    loading: false,
    error: null,
    currentItem: null
};

export const getConfigsAll: any = createAsyncThunk(
    "getConfigsAll",
    async (_:any,thunkAPI:any) => {
        try {
            const url:string = `${URL}-all`;
            const resp:any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getConfigs: any = createAsyncThunk(
    "getConfigs",
    async (
        data: {
            pageNumber: number;
            perPage: number;
            params:any,
            sort:any,
            filter:any
        },
        thunkAPI:any
    ) => {
        try {
            const { key, value, type, group } = data.params;
            const sort = data.sort;

            let url = `${URL}?page=${data.pageNumber}&perPage=${data.perPage}`;
            const searchParams = [];

            if (key) {
                searchParams.push(`key:${key}`);
            }
            if (value) {
                searchParams.push(`value:${value}`);
            }
            if (type) {
                searchParams.push(`type:${type}`);
            }
            if (group) {
                searchParams.push(`group:${group}`);
            }

            if (searchParams.length > 0) {
                url += `&search=${searchParams.join(';')}`;
            }

            const orderBy = [];
            const sortedBy = [];

            for (const [field, order] of Object.entries(sort)) {
                orderBy.push(field);
                sortedBy.push(order === 'ascend' ? 'asc' : 'desc');
            }

            if (orderBy.length > 0) {
                url += `&orderBy=${orderBy.join(',')}&sortedBy=${sortedBy.join(',')}`;
            }
            const joint = "&searchJoin=and";
            url += joint;
            const resp:any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const storeConfig: any = createAsyncThunk(
    "storeConfig",
    async (data:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}`;
            const resp:any = await api.post(url, data);
            return resp.data;
        }  catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            }  else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);


export const updateConfig: any = createAsyncThunk(
    "updateConfig",
    async (data: any, thunkAPI:any) => {
        try {
            const { id, ...payload } = data;
            const url:string = `${URL}/${id}`;
            const resp:any = await api.put(url, payload);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const deleteConfig: any = createAsyncThunk(
    "deleteConfig",
    async (id:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}/${id}`;
            const resp:any = await api.delete(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data.message);
            }  else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

const configSlice = createSlice({
    name: 'config',
    initialState,
    reducers: {
        setCurrentItem: (state, action) => {
            state.currentItem = action.payload;
        },
        clearError: (state) => {
            state.error = null;
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(getConfigsAll.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getConfigsAll.fulfilled, (state, action) => {
                state.loading = false;
                state.items = action.payload;
            })
            .addCase(getConfigsAll.rejected, (state:any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })
    }
});

export const { setCurrentItem, clearError } = configSlice.actions;
export default configSlice.reducer;
