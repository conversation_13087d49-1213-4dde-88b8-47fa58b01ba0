{"messages": {"success": "Success", "error": "An error occurred", "loading": "Loading..."}, "login": {"welcome": "Welcome back !", "subtitle": "Sign in to your account", "tooltip": "Choose your preferred login method", "email": "Email", "phone": "Phone", "cin": "ID Card", "password": "Password", "signIn": "Sign In", "forgotPassword": "Forgot Password?", "or": "OR", "noAccount": "Don't have an account?", "signUp": "Sign up now", "validation": {"required": "This field is required", "email": "Please enter a valid email address", "phone": "Phone number must be 8 digits", "cin": "CIN must be 8 digits"}, "placeholder": {"email": "Enter your email", "phone": "Enter your phone number", "cin": "Enter your CIN", "password": "Enter your password"}}, "register": {"welcome": "Welcome !", "step1Title": "Personal Information", "step2Title": "Phone Verification", "step3Title": "Address", "step4Title": "Account Details", "step5Title": "Email Verification", "step6Title": "Terms and Conditions", "subtitle": "Create an account to get started.", "tooltip": "Enter your details carefully.", "placeholder": {"email": "Email address", "password": "Password", "confirmPassword": "Confirm Password", "nom": "Last Name", "prenom": "First Name", "telephone": "Phone Number", "adresse": "Address", "cin": "CIN"}, "validation": {"required": "This field is required.", "email": "Please enter a valid email address.", "phone": "Phone number must be 8 digits", "cin": "CIN must be 8 digits", "passwordMismatch": "Passwords do not match.", "acceptTerms": "You must accept the terms and conditions.", "passwordLength": "Password must be at least 8 characters long", "passwordComplexity": "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character"}, "acceptTerms": "I accept the", "termsLink": "terms and conditions", "next": "Next", "previous": "Back", "signUp": "Sign Up", "or": "or", "haveAccount": "Already have an account?", "signIn": "Sign In", "resendCode": "Resend code", "phoneValidated": "Phone number validated", "emailValidated": "Email validated", "termsAgreement": "I agree to the", "retryIn": "<PERSON><PERSON><PERSON><PERSON> dans", "seconds": "secondes", "OTPPhoneTitle": "Enter the OTP Code", "OTPPhoneDescription": "Please enter the OTP code sent to your phone number", "OTPEmailTitle": "Enter the OTP Code", "OTPEmailDescription": "Please enter the OTP code sent to your email address"}, "reset": {"requestTitle": "Reset Your Password", "requestDescription": "Enter your email to receive a reset code and follow the steps.", "step1Title": "<PERSON><PERSON>", "step2Title": "Verify OTP", "step3Title": "Set New Password", "emailPlaceholder": "Enter your email", "resendCode": "Resend Code", "retryIn": "Retry in", "seconds": "seconds", "haveAccount": "Already have an account?", "signIn": "Sign In", "next": "Next", "previous": "Previous", "confirm": "Confirm", "validation": {"emailRequired": "Please enter your email.", "emailValid": "Please enter a valid email address.", "invalidOtp": "Invalid OTP code.", "passwordRequired": "Please enter your password.", "passwordLength": "Password must be at least 8 characters.", "confirmRequired": "Please confirm your password.", "passwordMatch": "Passwords do not match.", "passwordComplexity": "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character"}, "EmailValidated": "Email has been successfully validated.", "newPasswordTitle": "Set Your New Password", "newPasswordDescription": "Enter and confirm your new password.", "passwordPlaceholder": "New Password", "confirmPlaceholder": "Confirm Password", "OTPEmailTitle": "Enter the OTP Code", "OTPEmailDescription": "We have sent an OTP code to your email. Please enter it below to verify your email."}, "access_denied": {"title": "Access Denied", "sub_title": "Sorry, you do not have permission to access this page.", "button": "Return to Home"}, "not_found": {"title": "Page Not Found", "sub_title": "Sorry, the page you visited does not exist.", "button": "Back to Home"}, "auth_header": {"profile": "Profile", "settings": "Settings", "logout": "Logout"}, "auth_sidebar": {"dashboard": "Dashboard", "roles_permissions": "Roles and Permissions", "settings": "Settings", "users_section": "Manage Users", "settings_section": "Subscription Settings", "manage_admins": "Manage Admins", "manage_clients": "Manage Clients", "manage-governorates": "manage Governorates", "manage_delegations": "Manage Delegations", "manage_stations": "Manage stops", "manage_routes": "Manage routes", "manage_establishments": "Manage Establishments", "manage_abnTypes": "Manage Subscription Types", "manage_abnSubTypes": "Manage Subscription Subtypes", "manage_baseTariff": "Manage Base Tariff", "manage_civilSubscriptions": "Civil Subscriptions", "manage_subscriptions": "Subscriptions", "new_subscription": "New Subscription", "my_subscriptions": "My Subscriptions"}, "dashboard": {"Statistiques_A1": "Statistics A1", "Statistiques_A2": "Statistics A2", "active_plans": "Active Plans", "expiring": "Expiring Soon", "revenue": "Revenue", "total_subscribers": "Total Subscribers"}, "manage_profile": {"welcome": "Welcome", "manage_your_informations": "Manage your profile securely", "personal_informations": "Personal Informations", "firstname": "First Name", "lastname": "Last Name", "email": "Email", "phone": "Phone", "cin": "CIN", "address": "Address", "old_password": "Current Password", "new_password": "New Password", "confirm_password": "Confirm Password", "update": "Update", "update_email": "Update Email", "change_password": "Change Password", "requestTitle": "Change Your Email Address", "requestDescription": "Enter your current email address, and we'll send you a code to reset it.", "oldEmailPlaceholder": "Current Email Address", "newEmailTitle": "New Email Address", "newEmailDescription": "Enter your new email address, and we'll send you a code to verify it.", "newEmailPlaceholder": "New Email Address", "confirmPlaceholder": "Confirm Password", "resendCode": "Resend Code", "retryIn": "Retry in", "seconds": "seconds", "haveAccount": "Already have an account?", "signIn": "Sign In", "next": "Next", "previous": "Previous", "confirm": "Confirm", "validation": {"emailRequired": "Please enter your email address.", "emailValid": "Please enter a valid email address.", "invalidOtp": "Invalid OTP. Please try again.", "passwordRequired": "Password is required.", "passwordLength": "Password must be at least 8 characters long.", "confirmRequired": "Please confirm your password.", "passwordMatch": "Passwords do not match.", "passwordComplexity": "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character"}, "EmailValidated": "Email validated successfully!", "OTPEmailTitle": "Enter the confirmation code", "OTPEmailDescription": "We have sent a code to your email. Please enter it below to verify your email."}, "subscriptions": {"search": "Search", "state": "State", "type": "Type", "results": "Results", "reset": "Reset", "upcoming": "Upcoming", "available": "Available", "bought": "Already Bought", "in": "in", "days": "days", "not_available": "Not Available", "lastSubscription": "Your Last Subscription", "payNow": "Pay Now", "noPhoto": "No Photo", "title": "School Subscriptions", "previous": "Previous", "next": "Next", "confirm": "Confirm", "transportCard": "Transport Card", "validityInfo": "This card is valid only with ID. Not transferable.", "validation": {"required": "This field is required", "cin": "CIN must be 8 digits", "userNotFound": "User not found, please check your identity number or date of birth"}, "placeholders": {"cin": "Enter your CIN", "birthdate": "Enter your birthdate", "grade_level": "Enter grade level", "school_year": "Ex: 2024-2025", "period": "Select a period", "housing_delegation": "Select a delegation", "station_depart": "Select a departure station", "station_arrival": "Select an arrival station", "line": "Select a line", "affaire_sociale_id": "Enter the ID"}, "labels": {"cin": "CIN", "parent_cin": "Parent CIN", "unique_student_ID": "Unique Student ID", "birthdate": "Date of Birth", "lastname": "Last Name", "firstname": "First Name", "gender": "Gender", "delegation_of_establishment": "Delegation of Establishment", "establishment": "Establishment", "grade_level": "Grade Level", "school_year": "School Year", "period": "Period", "semester1": "Semester 1", "semester2": "Semester 2", "annual": "Annual", "housing_delegation": "Housing Delegation", "station_depart": "Departure Station", "station_arrival": "Arrival Station", "line": "Line", "hasVacation": "Vacation", "isSocialAffairs": "Social Affairs", "affaire_sociale_id": "Social Affairs ID", "priceGlobal": "Total Price"}, "options": {"male": "Male", "female": "Female", "withVacation": "With Vacation", "withoutVacation": "Without Vacation", "withSocialAffairs": "With Social Affairs", "withoutSocialAffairs": "Without Social Affairs"}, "currency": "TND", "uploadPhoto": "Upload Photo", "cropImage": "Crop Image", "confirmCrop": "Confirm Crop", "cancel": "Cancel"}, "manage_subscription": {"title": "Subscription Management", "add": "Add Subscription", "edit": "Edit Subscription", "details": "Subscription Details", "save": "Save", "successMessage": "Operation completed successfully!", "upload": "Upload Photo", "cropImage": "Crop Photo", "noPhoto": "No Photo", "backgroundRequirement": "The image must have a white background", "selectSubscriptionPrompt": "Please select a subscription type", "confirmCrop": "Save", "cancel": "Cancel", "modal": {"cardTitle": "Subscription Card", "print": "Print", "close": "Close"}, "card": {"title": "Member Card", "name": "Name", "number": "Card Number", "expiry": "Expiry Date", "footerText": "Thank you for being a valued member!"}, "restDays": {"1": "1 day", "2": "2 days", "3": "3 days", "4": "4 days", "5": "5 days"}, "tooltips": {"payment": "Manage Payment", "renew": "Renew Subscription", "viewCard": "View Subscription Card"}, "paymentOptions": {"notPayed": "Unpaid", "payed": "Paid", "expired": "Expired"}, "options": {"withVacation": "With Vacation", "withoutVacation": "Without Vacation"}, "filters": {"client": "Search by Subscriber"}, "labels": {"id": "ID", "restDays": "Number of Rest Days", "socialAffair": "Social Affair", "periodicity": "Periodicity", "subsNumber": "Number of Subscribers", "governorate_depart": "Departure Governorate", "governorate_arrival": "Arrival Governorate", "delegation_depart": "Departure Delegation", "station_depart": "Departure Station", "delegation_arrival": "Arrival Delegation", "station_arrival": "Arrival Station", "image": "Image", "createdAt": "Created At", "clientCin": "Subscriber by CIN", "client": "Subscriber", "matriculate": "Matriculation", "clientDob": "Date of Birth", "line": "Line", "status": "Status", "conventionNumber": "Convention Number", "vacation": "Vacation", "totalAmount": "Total Amount", "actions": "Actions"}, "errors": {"clientRequired": "Number of subscribers is required", "matriculateRequired": "Matriculation is required", "clientDobRequired": "Date of birth is required", "subsNumberRequired": "Subscriber is required", "restDaysRequired": "Number of rest days is required", "isStagiaRequired": "Stagia is required", "photoRequired": "Photo is required", "periodicityRequired": "Periodicity is required", "lineRequired": "Select a line", "vacationRequired": "Please indicate if the subscription includes vacation.", "governorateRequired": "Governorate is required.", "delegationDepartRequired": "Departure delegation is required", "delegationArrivalRequired": "Arrival delegation is required", "stationDepartRequired": "Departure station is required", "stationArrivalRequired": "Arrival station is required", "socialAffairRequired": "Social affair is required"}, "placeholders": {"selectClient": "Select a subscriber", "matriculate": "Enter matriculation", "clientDob": "Enter date of birth", "subsNumber": "Enter number of subscribers", "restDays": "Select number of rest days", "periodicity": "Select a periodicity", "line": "Select a line", "delegation": "Select a delegation", "governorate": "Select a governorate", "station_depart": "Select departure station", "station_arrival": "Select arrival station", "socialAffair": "Select social affair"}, "confirmDelete": "Are you sure you want to delete this subscription?", "yes": "Yes", "no": "No"}}