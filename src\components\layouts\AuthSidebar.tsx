import React from 'react';
import {Layout, <PERSON>u, But<PERSON>, theme} from 'antd';
import { useLocation, useNavigate } from 'react-router-dom';
import {
    LayoutDashboard,
    X,
    IdCard,
    List
} from 'lucide-react';
import {assets} from "../../assets/assets.ts";
import {useTranslation} from "react-i18next";
import {useSelector} from "react-redux";

const { Sider } = Layout;

interface SidebarProps {
    collapsed: boolean;
    onCollapse: (collapsed: boolean) => void;
    isMobile: boolean;
}

const AuthSidebar: React.FC<SidebarProps> = ({ collapsed, onCollapse, isMobile }) => {
    const navigate = useNavigate();
    const location = useLocation();
    const { token } = theme.useToken();
    const {t} = useTranslation();
    const { permissions } = useSelector((state: any) => state.auth);


    const allMenuItems : any[] = [
        {
            key: '/auth/new-subscription',
            icon: <IdCard className="w-5 h-5" />,
            label: t("auth_sidebar.new_subscription"),
        },
        {
            key: '/auth/my-subscriptions',
            icon: <List className="w-5 h-5" />,
            label: t("auth_sidebar.my_subscriptions"),
        },
    ];

    const filteredMenuItems: any[] = allMenuItems.filter((item:any) =>
        Array.isArray(permissions) && permissions.includes(item.permission)
    );

    if (isMobile && !collapsed) {
        return (
            <div className="fixed inset-0 z-50 bg-black bg-opacity-50">
                <div
                    className="fixed inset-y-0 left-0 w-64 bg-white"
                    style={{
                        boxShadow: token.boxShadow,
                        transition: `all ${token.motionDurationMid}`,
                    }}
                >
                    <div className="p-4">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center justify-content-center gap-8">
                                <span className="text-xl font-bold" style={{color: token.colorTextHeading}}>
                                    <img src={assets.logo2} alt="logo" width={150}/>
                                </span>
                            </div>
                            <Button
                                type="text"
                                icon={<X className="w-5 h-5"/>}
                                onClick={() => onCollapse(true)}
                                className="flex items-center justify-center"
                            />
                        </div>
                    </div>
                    <Menu
                        mode="inline"
                        selectedKeys={[allMenuItems.find(item => location.pathname.startsWith(item.key))?.key || location.pathname]}
                        items={filteredMenuItems}
                        onClick={({ key }) => {
                            navigate(key);
                            if (isMobile) onCollapse(true);
                        }}
                        style={{ borderRight: 'none' }}
                    />
                </div>
            </div>
        );
    }

    return (
         <Sider
            width={260}
            collapsible
            collapsed={collapsed}
            onCollapse={onCollapse}
            trigger={null}
            collapsedWidth={isMobile ? 0 : 80}
            className="hidden  md:block"
            style={{
                background: token.colorBgContainer,
            }}
            >
            <div className="border-r-2">
                <div className={`flex items-center justify-center`}>
                {!collapsed ? (
                    <div className="p-4 flex items-center ">
                    <img src={assets.logoSrtgn} alt="logo" width={150} />
                    </div>
                ) : (
                    <div className="p-3">
                    <img
                        style={{ paddingTop: "2px" }}
                        src={assets.logo}
                        alt="logo"
                        width={40}
                        height={40}
                    />
                    </div>
                )}
                </div>
            </div>
            <Menu
                mode="inline"
                className={!collapsed ? "p-2" : ""}
                selectedKeys={[location.pathname]}
                // TODO : change it by finalFilteredItems
                items={allMenuItems}
                onClick={({ key }) => navigate(key)}
                style={{
                borderRight: "none",
                height: "89vh",
                overflowY: "scroll",
                }}
            />
            </Sider>
    );
};

export default AuthSidebar;