import React, { useState, useEffect } from 'react';
import { Layout, Button, theme } from 'antd';
import { Outlet } from 'react-router-dom';
import { Menu as MenuIcon } from 'lucide-react';
import {AuthHeader, AuthSidebar} from "../components";



const { Content } = Layout;

const AuthLayout: React.FC = () => {
    const [collapsed, setCollapsed] = useState(false);
    const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
    const { token } = theme.useToken();

    useEffect(() => {
        const handleResize = () => {
            const mobile = window.innerWidth < 768;
            setIsMobile(mobile);
            if (!mobile && collapsed) {
                setCollapsed(false);
            }
        };

        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, [collapsed]);

    return (
        <Layout style={{ minHeight: '80vh' }}>
            <AuthSidebar
                collapsed={collapsed}
                onCollapse={setCollapsed}
                isMobile={isMobile}
            />
            <Layout>
                <AuthHeader>
                    <Button
                        type="text"
                        icon={<MenuIcon className="w-5 h-5" />}
                        onClick={() => setCollapsed(!collapsed)}
                        className="mr-4 flex items-center justify-center"
                    />
                </AuthHeader>
                <Content
                    style={{
                        padding: token.paddingLG,
                        background: token.colorBgContainer,
                        height: "80vh",
                        overflowY: "scroll",
                    }}
                >
                    <Outlet />
                </Content>
            </Layout>
        </Layout>
    );
};

export default AuthLayout;