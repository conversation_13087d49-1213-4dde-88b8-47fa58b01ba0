import { useState } from "react";
import { Button, Form, Input, Steps } from "antd";
import { useTranslation } from "react-i18next";
import { Mail } from "lucide-react";
import { assets } from "../../assets/assets.ts";
import { OTPProps } from "antd/es/input/OTP";
import { updateEmail, updateEmailStepOne, updateEmailStepThree, updateEmailStepTwo } from "../../features/auth/authSlice.ts";
import { useDispatch } from "react-redux";
import { toast, ToastContainer } from "react-toastify";
import { AppDispatch } from "../../features/store.ts";
const { Step } = Steps;

const UpdateEmailForm = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [countdownEmail, setCountdownEmail] = useState(0);
  const [isEmailLinkActive, setIsEmailLinkActive] = useState(true);
  const [otpEmailValue, setOtpEmailValue] = useState("");
  const [otpEmailError, setOtpEmailError] = useState(false);
  const [isEmailValidated, setIsEmailValidated] = useState(false);

  const [countdownNewEmail, setCountdownNewEmail] = useState(0);
  const [isNewEmailLinkActive, setIsNewEmailLinkActive] = useState(true);
  const [otpNewEmailValue, setOtpNewEmailValue] = useState("");
  const [otpNewEmailError, setOtpNewEmailError] = useState(false);
  const [isNewEmailValidated, setIsNewEmailValidated] = useState(false);
  const [allFormFields, setAllFormFields] = useState({});

  const onChangeOtpEmail: OTPProps["onChange"] = (text) => {
    setOtpEmailError(false);
    setOtpEmailValue(text);
  };

  const onChangeOtpNewEmail: OTPProps["onChange"] = (text) => {
    setOtpNewEmailError(false);
    setOtpNewEmailValue(text);
  };

  // Fonction pour renvoyer le code par téléEmail
  const handleResendEmailCode = () => {
    // Logique pour renvoyer le code par téléEmail
    setIsEmailLinkActive(false);
    setCountdownEmail(60); // Démarre un compte à rebours de 60 secondes
    // Logique d'envoi du code (par exemple, via une API)
    const interval = setInterval(() => {
      setCountdownEmail((prev: any) => {
        if (prev === 1) {
          clearInterval(interval);
          setIsEmailLinkActive(true);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
    // handle step 1
    dispatch(updateEmailStepOne(allFormFields)).unwrap();
  };

  const handleResendNewEmailCode = () => {
    // Logique pour renvoyer le code par téléEmail
    setIsNewEmailLinkActive(false);
    setCountdownNewEmail(60); // Démarre un compte à rebours de 60 secondes
    // Logique d'envoi du code (par exemple, via une API)
    const interval = setInterval(() => {
      setCountdownNewEmail((prev: any) => {
        if (prev === 1) {
          clearInterval(interval);
          setIsNewEmailLinkActive(true);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
      // handle step 3
      dispatch(updateEmailStepThree(allFormFields)).unwrap();
  };

  const onFinish = async () => {
    setLoading(true);
    await dispatch(updateEmail({...allFormFields, newEmailVerificationCode: otpNewEmailValue})).unwrap().then((response: any) => {
      toast.success("Email mis à jour avec succès !", { position: "top-center" });
      window.location.reload();
    }).catch((error: any) => {
      setOtpNewEmailError(true);
      if (navigator.vibrate) {
        navigator.vibrate(500);
      }
      toast.error(error.message, { position: "top-center" });
      setLoading(false);
    })
  };

  const steps = [
    {
      title: t("manage_profile.requestTitle"),
      content: (
        <>
          <h2 className="text-2xl text-center font-bold text-gray-800 mb-2">
            {t("manage_profile.requestTitle")}
          </h2>
          <p className="text-gray-600 text-center mb-6">
            {t("manage_profile.requestDescription")}
          </p>
          <Form.Item
            name="oldEmail"
            rules={[
              {
                required: true,
                message: t("manage_profile.validation.emailRequired"),
              },
              {
                type: "email",
                message: t("manage_profile.validation.emailValid"),
              },
            ]}
          >
            <Input
              prefix={<Mail className="text-gray-400" size={18} />}
              placeholder={t("manage_profile.oldEmailPlaceholder")}
              className="text-sm h-12"
              disabled={isEmailValidated}
            />
          </Form.Item>
        </>
      ),
    },
    {
      title: t("manage_profile.OTPEmailTitle"),
      content: (
        <>
          <h2 className="text-2xl text-center font-bold text-gray-800 mb-2">
            {t("manage_profile.OTPEmailTitle")}
          </h2>
          <p className="text-gray-600 text-center">
            {t("manage_profile.OTPEmailDescription")}
          </p>
          <Form.Item validateStatus={otpEmailError ? "error" : ""}>
            <div className="lg:px-16 sm:px-3 pt-6">
              {isEmailValidated ? (
                <div className="text-sm p-6 border-2 border-gray-100 rounded-lg shadow-sm">
                  <div className="flex justify-center items-center">
                    <img src={assets.validatedGif} alt="GIF" width={70} />
                  </div>
                  <div className="flex items-center justify-center text-red-600">
                    {t("manage_profile.EmailValidated")}
                  </div>
                </div>
              ) : (
                <div className="text-center justify-center items-center">
                  <Input.OTP
                    length={8}
                    size="middle"
                    className={`justify-center ${
                      otpEmailError ? "border-red-500" : ""
                    }`}
                    autoFocus
                    onChange={onChangeOtpEmail}
                  />

                  <div className="mt-3 flex text-center justify-center items-center">
                    <span>
                      {isEmailLinkActive ? (
                        <Button
                          type="link"
                          className="text-red-500 text-sm"
                          onClick={handleResendEmailCode}
                        >
                          {t("manage_profile.resendCode")}
                        </Button>
                      ) : (
                        <span>
                          {t("manage_profile.retryIn")}{" "}
                          <span className="font-semibold">
                            {countdownEmail}
                          </span>{" "}
                          {t("manage_profile.seconds")}
                        </span>
                      )}
                    </span>
                  </div>
                </div>
              )}
            </div>
          </Form.Item>
        </>
      ),
    },
    {
      title: t("manage_profile.newEmailTitle"),
      content: (
        <>
          <h2 className="text-2xl text-center font-bold text-gray-800 mb-2">
            {t("manage_profile.newEmailTitle")}
          </h2>
          <p className="text-gray-600 text-center mb-6">
            {t("manage_profile.newEmailDescription")}
          </p>
          <Form.Item
            name="newEmail"
            rules={[
              {
                required: true,
                message: t("manage_profile.validation.emailRequired"),
              },
              {
                type: "email",
                message: t("manage_profile.validation.emailValid"),
              },
            ]}
          >
            <Input
              prefix={<Mail className="text-gray-400" size={18} />}
              placeholder={t("manage_profile.newEmailPlaceholder")}
              className="text-sm h-12"
            />
          </Form.Item>
        </>
      ),
    },
    {
      title: t("manage_profile.OTPNewEmailTitle"),
      content: (
        <>
          <h2 className="text-2xl text-center font-bold text-gray-800 mb-2">
            {t("manage_profile.OTPNewEmailTitle")}
          </h2>
          <p className="text-gray-600 text-center">
            {t("manage_profile.OTPNewEmailDescription")}
          </p>
          <Form.Item validateStatus={otpNewEmailError ? "error" : ""}>
            <div className="lg:px-16 sm:px-3 pt-6">
              {isNewEmailValidated ? (
                <div className="text-sm p-6 border-2 border-gray-100 rounded-lg shadow-sm">
                  <div className="flex justify-center items-center">
                    <img src={assets.validatedGif} alt="GIF" width={70} />
                  </div>
                  <div className="flex items-center justify-center text-red-600">
                    {t("manage_profile.NewEmailValidated")}
                  </div>
                </div>
              ) : (
                <div className="text-center justify-center items-center">
                  <Input.OTP
                    length={8}
                    size="middle"
                    className={`justify-center ${
                      otpNewEmailError ? "border-red-500" : ""
                    }`}
                    autoFocus
                    onChange={onChangeOtpNewEmail}
                  />

                  <div className="mt-3 flex text-center justify-center items-center">
                    <span>
                      {isNewEmailLinkActive ? (
                        <Button
                          type="link"
                          className="text-red-500 text-sm"
                          onClick={handleResendNewEmailCode}
                        >
                          {t("manage_profile.resendCode")}
                        </Button>
                      ) : (
                        <span>
                          {t("manage_profile.retryIn")}{" "}
                          <span className="font-semibold">
                            {countdownNewEmail}
                          </span>{" "}
                          {t("manage_profile.seconds")}
                        </span>
                      )}
                    </span>
                  </div>
                </div>
              )}
            </div>
          </Form.Item>
        </>
      ),
    },
  ];

  const next = () => {
    setLoading(true);
    form
      .validateFields()
      .then(() => {
        switch (currentStep) {
          case 0:
            // handle step 1
            dispatch(updateEmailStepOne(form.getFieldsValue()))
              .unwrap()
              .then((response: any) => {
                setAllFormFields(form.getFieldsValue());
                setCurrentStep((prev: any) => prev + 1);
                setLoading(false);
              })
              .catch((error: any) => {
                handleInputErrors(error);
                toast.error(error.message);
                setLoading(false);
              });
            break;
          case 1:
             // handle step 2
             dispatch(updateEmailStepTwo({...allFormFields, oldEmailVerificationCode: otpEmailValue}))
             .unwrap()
             .then((response: any) => {
               setAllFormFields({...allFormFields, oldEmailVerificationCode: otpEmailValue});
               setIsEmailValidated(true);
               setCurrentStep((prev: any) => prev + 1);
               setLoading(false);
              })
             .catch((error: any) => {
              setOtpEmailError(true);
               if (navigator.vibrate) {
                 navigator.vibrate(500);
               }
               toast.error(error.message);
               setLoading(false);
              });
           break;
          case 2:
            // handle step 3
            dispatch(updateEmailStepThree({...allFormFields, ...form.getFieldsValue()}))
            .unwrap()
            .then((response: any) => {
              setAllFormFields({...allFormFields, ...form.getFieldsValue()});
              setCurrentStep((prev: any) => prev + 1);
              setLoading(false);
            })
            .catch((error: any) => {
              handleInputErrors(error);
              toast.error(error.message);
              setLoading(false);
            });
          break;
        }
      })
      .catch((errorInfo: any) => {
        console.log("Validation Failed:", errorInfo);
        setLoading(false);
      });
  };

  // Handle input errors messages
  const handleInputErrors: any = (error: any) => {
    const apiErrors = error.errors;
    // Transformez en tableau pour setFields
    const fields = Object.entries(apiErrors).map(([fieldName, messages]) => ({
      name: fieldName,
      errors: messages as string[],
    }));
    form.setFields(fields);
  };

  const prev = () => {
    // setOtpEmailValue("");
    if (currentStep === 0 || (currentStep === 1 && isEmailValidated)) {
      return; // Empêche de revenir à l'étape précédente si email validé
    }
    setCurrentStep((prev: any) => prev - 1);
  };
  const isPrevDisabled =
    currentStep === 0 ||
    (currentStep === 1 && isEmailValidated) ||
    (currentStep === 3 && isNewEmailValidated);

  return (
    <>
      <ToastContainer/>
      <Steps current={currentStep} className="mb-8 mt-8">
        {steps.map((item, index) => (
          <Step key={index} />
        ))}
      </Steps>

      <Form
        form={form}
        name="reset"
        initialValues={{ remember: true }}
        onFinish={onFinish}
        layout="vertical"
        size="large"
        onValuesChange={(changedValues) => {
          const fieldName = Object.keys(changedValues)[0];
          form.setFields([{ name: fieldName, errors: [] }]);
        }}
      >
        {steps[currentStep].content}

        <div className="text-center !mt-8">
          {currentStep > 0 && (
            <Button
              disabled={isPrevDisabled || loading}
              type="link"
              onClick={prev}
              className="h-10 text-black"
            >
              {t("manage_profile.previous")}
            </Button>
          )}
          {currentStep === 0 ? (
            <Button type="primary" onClick={next} loading={loading}>
              {t("manage_profile.next")}
            </Button>
          ) : currentStep < steps.length - 1 ? (
            <Button
              type="link"
              onClick={next}
              className="h-10 text-red-500 !hover:text-red-500"
              disabled={loading}
            >
              {t("manage_profile.next")}
            </Button>
          ) : (
            <Button type="link" htmlType="submit" className="text-red-500" disabled={loading}>
              {t("manage_profile.confirm")}
            </Button>
          )}
        </div>
      </Form>
    </>
  );
};

export default UpdateEmailForm;
