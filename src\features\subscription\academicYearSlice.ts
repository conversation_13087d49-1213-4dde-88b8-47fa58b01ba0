import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import api from "../../config/axiosConfig.tsx";

const URL = '/academic-years';

// Define types for our state
interface AcademicYear {
    id: number;
    code: string;
    start_date: string;
    end_date: string;
    created_at?: string;
    updated_at?: string;
}

interface AcademicYearState {
    academicYears: AcademicYear[];
    academicYear: AcademicYear | null;
    loading: boolean;
    error: string | null;
    success: boolean;
}

const initialState: AcademicYearState = {
    academicYears: [],
    academicYear: null,
    loading: false,
    error: null,
    success: false
};

export const getAcademicYearsAll: any = createAsyncThunk(
    "getAcademicYearsAll",
    async (_:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}-all`;
            const resp:any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getAcademicYears: any = createAsyncThunk(
    "getAcademicYears",
    async (
        data: {
            pageNumber: number;
            perPage: number;
            params:any,
            sort:any,
            filter:any
        },
        thunkAPI:any
    ) => {
        try {
            const { code } = data.params;
            const sort = data.sort;

            let url = `${URL}?page=${data.pageNumber}&perPage=${data.perPage}`;
            const searchParams = [];

            if (code) {
                searchParams.push(`code:${code}`);
            }

            if (searchParams.length > 0) {
                url += `&search=${searchParams.join(';')}`;
            }

            const orderBy = [];
            const sortedBy = [];

            for (const [field, order] of Object.entries(sort)) {
                orderBy.push(field);
                sortedBy.push(order === 'ascend' ? 'asc' : 'desc');
            }

            if (orderBy.length > 0) {
                url += `&orderBy=${orderBy.join(',')}&sortedBy=${sortedBy.join(',')}`;
            }
            const joint = "&searchJoin=and";
            url += joint;
            const resp:any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getAcademicYear: any = createAsyncThunk(
    "getAcademicYear",
    async (id: number, thunkAPI:any) => {
        try {
            const url:string = `${URL}/${id}`;
            const resp:any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const storeAcademicYear: any = createAsyncThunk(
    "storeAcademicYear",
    async (data:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}`;
            const resp:any = await api.post(url, data);
            return resp.data;
        }  catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            }  else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const updateAcademicYear: any = createAsyncThunk(
    "updateAcademicYear",
    async (data: any, thunkAPI:any) => {
        try {
            const { id, ...payload } = data;

            const url:string = `${URL}/${id}`;
            const resp:any = await api.put(url, payload);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data);
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const deleteAcademicYear: any = createAsyncThunk(
    "deleteAcademicYear",
    async (id:any, thunkAPI:any) => {
        try {
            const url:string = `${URL}/${id}`;
            const resp:any = await api.delete(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = '/unauthorized';
                }
                return thunkAPI.rejectWithValue(data.message);
            }  else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

const academicYearSlice = createSlice({
    name: 'academicYear',
    initialState,
    reducers: {
        resetAcademicYearState: (state) => {
            state.academicYear = null;
            state.error = null;
            state.success = false;
        },
        clearAcademicYearErrors: (state) => {
            state.error = null;
        }
    },
    extraReducers: (builder) => {
        builder
            // Handle getAcademicYearsAll
            .addCase(getAcademicYearsAll.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getAcademicYearsAll.fulfilled, (state, action) => {
                state.loading = false;
                state.academicYears = action.payload;
                state.error = null;
            })
            .addCase(getAcademicYearsAll.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string;
            })

            // Handle getAcademicYears (paginated)
            .addCase(getAcademicYears.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getAcademicYears.fulfilled, (state) => {
                state.loading = false;
                state.error = null;
            })
            .addCase(getAcademicYears.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string;
            })

            // Handle getAcademicYear (single)
            .addCase(getAcademicYear.pending, (state) => {
                state.loading = true;
                state.academicYear = null;
                state.error = null;
            })
            .addCase(getAcademicYear.fulfilled, (state, action) => {
                state.loading = false;
                state.academicYear = action.payload;
                state.error = null;
            })
            .addCase(getAcademicYear.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string;
            })

            // Handle storeAcademicYear
            .addCase(storeAcademicYear.pending, (state) => {
                state.loading = true;
                state.success = false;
                state.error = null;
            })
            .addCase(storeAcademicYear.fulfilled, (state) => {
                state.loading = false;
                state.success = true;
                state.error = null;
            })
            .addCase(storeAcademicYear.rejected, (state, action) => {
                state.loading = false;
                state.success = false;
                state.error = action.payload as string;
            })

            // Handle updateAcademicYear
            .addCase(updateAcademicYear.pending, (state) => {
                state.loading = true;
                state.success = false;
                state.error = null;
            })
            .addCase(updateAcademicYear.fulfilled, (state) => {
                state.loading = false;
                state.success = true;
                state.error = null;
            })
            .addCase(updateAcademicYear.rejected, (state, action) => {
                state.loading = false;
                state.success = false;
                state.error = action.payload as string;
            })

            // Handle deleteAcademicYear
            .addCase(deleteAcademicYear.pending, (state) => {
                state.loading = true;
                state.success = false;
                state.error = null;
            })
            .addCase(deleteAcademicYear.fulfilled, (state) => {
                state.loading = false;
                state.success = true;
                state.error = null;
            })
            .addCase(deleteAcademicYear.rejected, (state, action) => {
                state.loading = false;
                state.success = false;
                state.error = action.payload as string;
            });
    }
});

export const { resetAcademicYearState, clearAcademicYearErrors } = academicYearSlice.actions;
export default academicYearSlice.reducer;

