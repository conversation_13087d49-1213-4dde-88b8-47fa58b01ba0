import { Form, Input, But<PERSON>, Row, Col, Avatar, Collapse } from "antd";
import { UserOutlined, PhoneOutlined, IdcardOutlined, EnvironmentOutlined, LockOutlined } from "@ant-design/icons";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { toast, ToastContainer } from "react-toastify";
import { AppDispatch, RootState } from "../../features/store.ts";
import {assets} from "../../assets/assets.ts";
import UpdateEmailForm from "./UpdateEmailForm.tsx";
import { useTranslation } from "react-i18next";
import { updatePassword, updateProfile } from "../../features/auth/authSlice.ts";

const { Panel } = Collapse;

const ManageProfile = () => {
    const { t } = useTranslation();
    const dispatch = useDispatch<AppDispatch>();
    const { user }:any = useSelector((state: RootState) => state.auth);
    const [form] = Form.useForm(); // pour la formulaire de information personnelle
    const [formUpdatePassword] = Form.useForm(); // pour la formulaire de modification de mot de passe
    const [loading, setLoading] = useState(false);
    const [activeKey, setActiveKey] = useState<string | string[]>(['1']); // Suivi de la clé active

    const onFinish = async (values: any) => {
        setLoading(true);
        try {
            await dispatch(updateProfile(values)).unwrap().then((response: any) => {
                localStorage.setItem('TOKEN', response.new_token);
                toast.success("Profil mis à jour avec succès !", { position: "top-center" });
            }).catch((error: any) => {
                handleInputErrors(form, error);
            })
        } catch (error) {
            toast.error("Échec de la mise à jour du profil", { position: "top-center" });
        } finally {
            setLoading(false);
        }
    };

    const onChangePassword = async (values: any) => {
        setLoading(true);
        try {
            await dispatch(updatePassword(values)).unwrap().then((response: any) => {
                toast.success("Mot de passe mis à jour avec succès !", { position: "top-center" });   
                formUpdatePassword.resetFields();
            }).catch((error: any) => {
                handleInputErrors(formUpdatePassword, error);
            });
        } catch (error) {
            toast.error("Échec de la mise à jour du mot de passe", { position: "top-center" });
        } finally {
            setLoading(false);
        }
    };

    const handleChange = (keys: string | string[]) => {
        const newKeys = Array.isArray(keys) ? keys : [keys];
        if (newKeys.length > 0) {
            setActiveKey(newKeys[0]);
        }
    };

    // Handle input errors messages
    const handleInputErrors: any = (form: any, error: any) => {
        const apiErrors = error.errors;
        // Transformez en tableau pour setFields
        const fields = Object.entries(apiErrors).map(([fieldName, messages]) => ({
        name: fieldName,
        errors: messages as string[],
        }));
        form.setFields(fields);
    };

    return (
        <div className="h-[80vh] flex items-center justify-center">
            <ToastContainer />
            <div className="w-full max-w-4xl px-4">
                <div className="px-4 rounded-2xl backdrop-blur-sm border-0">
                    <div
                        className="relative shadow p-6 bg-white rounded mb-3"
                        style={{
                            backgroundImage: `url(${assets.bg})`,
                            backgroundPosition: "center",
                            backgroundSize: "auto",
                            backgroundRepeat: "no-repeat",
                        }}
                    >
                        <div className="relative z-10 flex justify-between items-center">
                            <div className="flex items-center space-x-4">
                                <div className="relative group">
                                    <Avatar
                                        src={`https://ui-avatars.com/api/?name=${user?.firstname}+${user?.lastname}&background=333333&color=fff&bold=true`}
                                        size={64}
                                        className="relative transform transition duration-300 hover:scale-50 border-2 border-white"
                                        style={{
                                            borderRadius: "12px",
                                            clipPath: "polygon(0 0, 100% 0, 100% 80%, 95% 100%, 0 100%)",
                                        }}
                                    />
                                </div>

                                <div className="relative">
                                <h2 className="text-3xl font-extrabold">
                                        {t('manage_profile.welcome')},{" "}
                                        <span className="inline-block">
                                    {user?.lastname}!
                                </span>
                                    </h2>
                                    <p className="text-sm text-gray-900/50 mt-1 flex items-center">
                                        <span
                                            className="mr-2">{t('manage_profile.manage_your_informations')}</span>
                                        <svg
                                            style={{color: "var(--primary-color)"}}
                                            className="animate-bounce-slow w-4 h-4"
                                            fill="currentColor"
                                            viewBox="0 0 20 20"
                                        >
                                            <path
                                                d="M11 14.414V3a1 1 0 10-2 0v11.414l-3.293-3.293a1 1 0 00-1.414 1.414l5 5a1 1 0 001.414 0l5-5a1 1 0 00-1.414-1.414L11 14.414z"/>
                                        </svg>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <Collapse
                        activeKey={activeKey}
                        onChange={handleChange}
                        accordion
                    >
                        <Panel header={t('manage_profile.personal_informations')} key="1" className="rounded-xl">
                            <Form
                                form={form}
                                layout="vertical"
                                initialValues={user}
                                onFinish={onFinish}
                                className="space-y-4"
                                size="large"
                                onValuesChange={(changedValues) => {
                                    const fieldName = Object.keys(changedValues)[0];
                                    form.setFields([{ name: fieldName, errors: [] }]);
                                  }}
                            >
                                <Row gutter={[24, 8]}>
                                    <Col xs={24} md={12}>
                                        <Form.Item
                                            name="firstname"
                                            rules={[{required: true, message: "Le prénom est requis"}]}>
                                            <Input
                                                prefix={<UserOutlined className="text-gray-400"/>}
                                                placeholder={t('manage_profile.firstname')}
                                                size="large"
                                                className="rounded-lg hover:border-blue-300 focus:border-blue-500"
                                            />
                                        </Form.Item>
                                    </Col>

                                    <Col xs={24} md={12}>
                                        <Form.Item
                                            name="lastname"
                                            rules={[{required: true, message: "Le nom de famille est requis"}]}>
                                            <Input
                                                prefix={<UserOutlined className="text-gray-400"/>}
                                                placeholder={t('manage_profile.lastname')}
                                                size="large"
                                                className="rounded-lg hover:border-blue-300 focus:border-blue-500"
                                            />
                                        </Form.Item>
                                    </Col>

                                    <Col xs={24} md={12}>
                                        <Form.Item
                                            name="phone"
                                            rules={[{
                                                required: true,
                                                message: "Le numéro de téléphone est requis"
                                            }, {
                                                pattern: /^\d{8}$/,
                                                message: "Le numéro de téléphone doit être composé de 8 chiffres"
                                            }]}>
                                            <Input
                                                prefix={<PhoneOutlined className="text-gray-400"/>}
                                                placeholder={t('manage_profile.phone')}
                                                size="large"
                                                maxLength={8}
                                                className="rounded-lg hover:border-blue-300 focus:border-blue-500"
                                            />
                                        </Form.Item>
                                    </Col>

                                    <Col xs={24} md={12}>
                                        <Form.Item
                                            name="cin"
                                            rules={[{
                                                required: true,
                                                message: "Le numéro CIN est requis"
                                            }, {
                                                pattern: /^\d{8}$/,
                                                message: "Le CIN doit être composé de 8 chiffres"
                                            }]}>
                                            <Input
                                                prefix={<IdcardOutlined className="text-gray-400"/>}
                                                placeholder={t('manage_profile.cin')}
                                                size="large"
                                                maxLength={8}
                                                className="rounded-lg hover:border-blue-300 focus:border-blue-500"
                                            />
                                        </Form.Item>
                                    </Col>

                                    <Col span={24}>
                                        <Form.Item
                                            name="address"
                                            rules={[{required: true, message: "L'adresse complète est requise"}]}>
                                            <Input
                                                prefix={<EnvironmentOutlined className="text-gray-400"/>}
                                                placeholder={t('manage_profile.address')}
                                                size="large"
                                                className="rounded-lg hover:border-blue-300 focus:border-blue-500"
                                            />
                                        </Form.Item>
                                    </Col>
                                </Row>

                                <Form.Item className="text-center mt-8">
                                    <Button
                                        type="primary"
                                        htmlType="submit"
                                        loading={loading}
                                    >
                                        {t('manage_profile.update')}
                                    </Button>
                                </Form.Item>
                            </Form>
                        </Panel>
                        <Panel header={t('manage_profile.update_email')} key="2">
                            <UpdateEmailForm/>
                        </Panel>
                        <Panel header={t('manage_profile.change_password')} key="3" className="rounded-xl">
                            <Form
                                form={formUpdatePassword}
                                layout="vertical"
                                onFinish={onChangePassword}
                                className="space-y-2"
                                size="large"
                                onValuesChange={(changedValues) => {
                                    const fieldName = Object.keys(changedValues)[0];
                                    form.setFields([{ name: fieldName, errors: [] }]);
                                  }}
                            >
                                <Form.Item
                                    name="oldPassword"
                                    rules={[{required: true, message: t('manage_profile.validation.passwordRequired')}]}>
                                    <Input.Password
                                        prefix={<LockOutlined className="text-gray-400"/>}
                                        placeholder={t('manage_profile.old_password')}
                                        size="large"
                                        className="rounded-lg"
                                    />
                                </Form.Item>

                                <Form.Item
                                    name="password"
                                    rules={[
                                        { required: true, message: t('manage_profile.validation.passwordRequired') },
                                        { min: 8, message: t('manage_profile.validation.passwordLength') },
                                        { pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[+-.!@\$%\^&\*()])/, message: t('manage_profile.validation.passwordComplexity') },
                                    ]}
                                    >
                                    <Input.Password
                                        prefix={<LockOutlined className="text-gray-400"/>}
                                        placeholder={t('manage_profile.new_password')}
                                        size="large"
                                        className="rounded-lg"
                                    />
                                </Form.Item>

                                <Form.Item
                                    name="confirmPassword"
                                    dependencies={['password']}
                                    rules={[{
                                        required: true,
                                        message: "La confirmation du mot de passe est requise"
                                    }, ({getFieldValue}) => ({
                                        validator(_, value) {
                                            if (!value || getFieldValue('password') === value) {
                                                return Promise.resolve();
                                            }
                                            return Promise.reject('Les mots de passe ne correspondent pas');
                                        },
                                    })
                                    ]}>
                                    <Input.Password
                                        prefix={<LockOutlined className="text-gray-400"/>}
                                        placeholder={t('manage_profile.confirm_password')}
                                        size="large"
                                        className="rounded-lg"
                                    />
                                </Form.Item>

                                <Form.Item className="text-center !mt-8">
                                    <Button
                                        htmlType="submit"
                                        loading={loading}
                                        type="primary"
                                    >
                                        {t('manage_profile.update')}
                                    </Button>
                                </Form.Item>
                            </Form>
                        </Panel>
                    </Collapse>
                </div>
            </div>
        </div>
    );
};

export default ManageProfile;
